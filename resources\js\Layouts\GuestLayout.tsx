import ApplicationLogo from '@/Components/ApplicationLogo';
import { Link } from '@inertiajs/react';
import { PropsWithChildren } from 'react';
import { HomeIcon } from '@heroicons/react/24/outline';

interface GuestLayoutProps extends PropsWithChildren {
    fullWidth?: boolean;
}

export default function Guest({ children, fullWidth = false }: GuestLayoutProps) {
    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 relative overflow-hidden">
            {/* Background Elements */}
            <div className="absolute inset-0 opacity-10">
                <div className="absolute top-10 left-10 w-32 h-32 bg-blue-300 rounded-full blur-xl"></div>
                <div className="absolute top-40 right-20 w-24 h-24 bg-indigo-300 rounded-full blur-lg"></div>
                <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-purple-300 rounded-full blur-2xl"></div>
                <div className="absolute bottom-10 right-10 w-28 h-28 bg-blue-400 rounded-full blur-lg"></div>
            </div>

            {/* Header */}
            <header className="bg-white shadow-sm border-b border-gray-200 relative z-10">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center py-4">
                        <Link href="/" className="flex items-center group">
                            <HomeIcon className="h-8 w-8 text-blue-600 mr-2 group-hover:scale-110 transition-transform duration-300" />
                            <h1 className="text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">PropertyHub</h1>
                        </Link>
                        <nav className="flex items-center space-x-4">
                            <Link
                                href="/properties"
                                className="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 hover:bg-blue-50"
                            >
                                Browse Properties
                            </Link>
                        </nav>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            {fullWidth ? (
                <main className="relative z-10">
                    {children}
                </main>
            ) : (
                <div className="flex min-h-[calc(100vh-80px)] flex-col items-center justify-center px-4 sm:px-6 lg:px-8 relative z-10">
                    <div className="w-full max-w-md">
                        {/* Logo */}
                        <div className="text-center mb-8">
                            <Link href="/" className="inline-flex items-center group">
                                <HomeIcon className="h-12 w-12 text-blue-600 mr-3 group-hover:scale-110 transition-transform duration-300" />
                                <h1 className="text-3xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">PropertyHub</h1>
                            </Link>
                            <p className="mt-2 text-gray-600">Your trusted property partner</p>
                        </div>

                        {/* Form Container */}
                        <div className="bg-white rounded-lg shadow-xl p-8 hover:shadow-2xl transition-shadow duration-300">
                            {children}
                        </div>

                        {/* Footer */}
                        <div className="text-center mt-6">
                            <p className="text-sm text-gray-500">
                                © 2024 PropertyHub. All rights reserved.
                            </p>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
