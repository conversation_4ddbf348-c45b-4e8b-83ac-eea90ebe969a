<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            'view properties',
            'create properties',
            'edit properties',
            'delete properties',
            'manage own properties',
            'view bookings',
            'create bookings',
            'manage bookings',
            'view messages',
            'send messages',
            'view reviews',
            'create reviews',
            'moderate reviews',
            'view payments',
            'process payments',
            'manage users',
            'view analytics',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions
        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo(Permission::all());

        $landlordRole = Role::create(['name' => 'landlord']);
        $landlordRole->givePermissionTo([
            'view properties',
            'create properties',
            'edit properties',
            'delete properties',
            'manage own properties',
            'view bookings',
            'manage bookings',
            'view messages',
            'send messages',
            'view reviews',
            'view payments',
            'process payments',
        ]);

        $agentRole = Role::create(['name' => 'agent']);
        $agentRole->givePermissionTo([
            'view properties',
            'create properties',
            'edit properties',
            'manage own properties',
            'view bookings',
            'manage bookings',
            'view messages',
            'send messages',
            'view reviews',
            'view payments',
        ]);

        $tenantRole = Role::create(['name' => 'tenant']);
        $tenantRole->givePermissionTo([
            'view properties',
            'create bookings',
            'view bookings',
            'view messages',
            'send messages',
            'create reviews',
            'view reviews',
            'view payments',
        ]);
    }
}
