<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Send notification to user
     */
    public function sendNotification($userId, $type, $title, $message, $data = null, $sendEmail = false)
    {
        try {
            // Create in-app notification
            $notification = Notification::createForUser($userId, $type, $title, $message, $data);

            // Send email notification if requested
            if ($sendEmail) {
                $user = User::find($userId);
                if ($user && $user->email) {
                    $this->sendEmailNotification($user, $title, $message);
                }
            }

            // TODO: Send push notification if user has enabled it
            // $this->sendPushNotification($userId, $title, $message);

            return $notification;
        } catch (\Exception $e) {
            Log::error('Failed to send notification: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Send booking notification
     */
    public function sendBookingNotification($booking, $type = 'created')
    {
        $property = $booking->property;
        $tenant = $booking->user;
        $landlord = $property->user;

        switch ($type) {
            case 'created':
                // Notify landlord about new booking
                $this->sendNotification(
                    $landlord->id,
                    'booking',
                    'New Booking Request',
                    "You have a new {$booking->type} booking request for {$property->title} from {$tenant->name}.",
                    ['booking_id' => $booking->id, 'property_id' => $property->id],
                    true
                );

                // Notify tenant about booking confirmation
                $this->sendNotification(
                    $tenant->id,
                    'booking',
                    'Booking Request Submitted',
                    "Your {$booking->type} booking request for {$property->title} has been submitted and is pending approval.",
                    ['booking_id' => $booking->id, 'property_id' => $property->id]
                );
                break;

            case 'confirmed':
                // Notify tenant about booking confirmation
                $this->sendNotification(
                    $tenant->id,
                    'booking',
                    'Booking Confirmed',
                    "Your {$booking->type} booking for {$property->title} has been confirmed!",
                    ['booking_id' => $booking->id, 'property_id' => $property->id],
                    true
                );
                break;

            case 'cancelled':
                // Notify tenant about booking cancellation
                $this->sendNotification(
                    $tenant->id,
                    'booking',
                    'Booking Cancelled',
                    "Your {$booking->type} booking for {$property->title} has been cancelled.",
                    ['booking_id' => $booking->id, 'property_id' => $property->id],
                    true
                );
                break;
        }
    }

    /**
     * Send payment notification
     */
    public function sendPaymentNotification($payment, $type = 'created')
    {
        $property = $payment->property;
        $tenant = $payment->user;
        $landlord = $property->user;

        switch ($type) {
            case 'created':
                // Notify tenant about payment creation
                $this->sendNotification(
                    $tenant->id,
                    'payment',
                    'Payment Created',
                    "Payment of ₱{$payment->amount} for {$property->title} has been created.",
                    ['payment_id' => $payment->id, 'property_id' => $property->id]
                );
                break;

            case 'paid':
                // Notify landlord about payment received
                $this->sendNotification(
                    $landlord->id,
                    'payment',
                    'Payment Received',
                    "Payment of ₱{$payment->amount} for {$property->title} has been received from {$tenant->name}.",
                    ['payment_id' => $payment->id, 'property_id' => $property->id],
                    true
                );

                // Notify tenant about payment confirmation
                $this->sendNotification(
                    $tenant->id,
                    'payment',
                    'Payment Confirmed',
                    "Your payment of ₱{$payment->amount} for {$property->title} has been confirmed.",
                    ['payment_id' => $payment->id, 'property_id' => $property->id],
                    true
                );
                break;

            case 'failed':
                // Notify tenant about payment failure
                $this->sendNotification(
                    $tenant->id,
                    'payment',
                    'Payment Failed',
                    "Your payment of ₱{$payment->amount} for {$property->title} has failed. Please try again.",
                    ['payment_id' => $payment->id, 'property_id' => $property->id],
                    true
                );
                break;
        }
    }

    /**
     * Send message notification
     */
    public function sendMessageNotification($message)
    {
        $sender = $message->sender;
        $receiver = $message->receiver;
        $property = $message->property;

        $propertyTitle = $property ? $property->title : 'a property';

        $this->sendNotification(
            $receiver->id,
            'message',
            'New Message',
            "You have a new message from {$sender->name} about {$propertyTitle}.",
            ['message_id' => $message->id, 'sender_id' => $sender->id, 'property_id' => $property?->id],
            true
        );
    }

    /**
     * Send review notification
     */
    public function sendReviewNotification($review, $type = 'created')
    {
        $property = $review->property;
        $reviewer = $review->user;
        $landlord = $property->user;

        switch ($type) {
            case 'created':
                // Notify landlord about new review
                $this->sendNotification(
                    $landlord->id,
                    'review',
                    'New Review Submitted',
                    "{$reviewer->name} has submitted a review for {$property->title}. It's pending your approval.",
                    ['review_id' => $review->id, 'property_id' => $property->id]
                );
                break;

            case 'approved':
                // Notify reviewer about review approval
                $this->sendNotification(
                    $reviewer->id,
                    'review',
                    'Review Approved',
                    "Your review for {$property->title} has been approved and is now visible to other users.",
                    ['review_id' => $review->id, 'property_id' => $property->id]
                );
                break;
        }
    }

    /**
     * Send email notification
     */
    private function sendEmailNotification($user, $title, $message)
    {
        try {
            // TODO: Implement email notification using Laravel Mail
            // For now, we'll just log it
            Log::info("Email notification sent to {$user->email}: {$title}");
        } catch (\Exception $e) {
            Log::error('Failed to send email notification: ' . $e->getMessage());
        }
    }

    /**
     * Send push notification
     */
    private function sendPushNotification($userId, $title, $message)
    {
        try {
            // TODO: Implement push notification using Pusher or Firebase
            // For now, we'll just log it
            Log::info("Push notification sent to user {$userId}: {$title}");
        } catch (\Exception $e) {
            Log::error('Failed to send push notification: ' . $e->getMessage());
        }
    }
}
