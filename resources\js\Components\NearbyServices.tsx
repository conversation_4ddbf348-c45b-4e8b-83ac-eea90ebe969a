import React, { useState, useEffect } from 'react';
import { 
    MapPinIcon, 
    AcademicCapIcon, 
    ShoppingBagIcon, 
    HeartIcon, 
    BuildingOffice2Icon,
    TruckIcon,
    StarIcon,
    ClockIcon,
    PhoneIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { gsap } from 'gsap';

interface NearbyService {
    id: string;
    name: string;
    type: 'school' | 'hospital' | 'shopping' | 'restaurant' | 'transport' | 'office';
    address: string;
    distance: number; // in kilometers
    rating?: number;
    reviews_count?: number;
    phone?: string;
    hours?: string;
    description?: string;
    image_url?: string;
}

interface NearbyServicesProps {
    propertyLatitude: number;
    propertyLongitude: number;
    propertyAddress: string;
    services?: NearbyService[];
}

const NearbyServices: React.FC<NearbyServicesProps> = ({
    propertyLatitude,
    propertyLongitude,
    propertyAddress,
    services = [],
}) => {
    const [activeFilter, setActiveFilter] = useState<string>('all');
    const [filteredServices, setFilteredServices] = useState<NearbyService[]>(services);

    useEffect(() => {
        // Animate services on load
        gsap.fromTo('.services-container',
            { y: 30, opacity: 0 },
            { y: 0, opacity: 1, duration: 0.6, ease: "power2.out" }
        );

        gsap.fromTo('.service-card',
            { scale: 0.9, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.4, stagger: 0.1, delay: 0.3, ease: "power2.out" }
        );
    }, []);

    useEffect(() => {
        if (activeFilter === 'all') {
            setFilteredServices(services);
        } else {
            setFilteredServices(services.filter(service => service.type === activeFilter));
        }
    }, [activeFilter, services]);

    const serviceTypes = [
        { key: 'all', label: 'All Services', icon: MapPinIcon, color: 'text-gray-600' },
        { key: 'school', label: 'Schools', icon: AcademicCapIcon, color: 'text-blue-600' },
        { key: 'hospital', label: 'Healthcare', icon: HeartIcon, color: 'text-red-600' },
        { key: 'shopping', label: 'Shopping', icon: ShoppingBagIcon, color: 'text-green-600' },
        { key: 'transport', label: 'Transport', icon: TruckIcon, color: 'text-purple-600' },
        { key: 'office', label: 'Business', icon: BuildingOffice2Icon, color: 'text-indigo-600' },
    ];

    const getServiceIcon = (type: string) => {
        switch (type) {
            case 'school':
                return AcademicCapIcon;
            case 'hospital':
                return HeartIcon;
            case 'shopping':
                return ShoppingBagIcon;
            case 'transport':
                return TruckIcon;
            case 'office':
                return BuildingOffice2Icon;
            default:
                return MapPinIcon;
        }
    };

    const getServiceColor = (type: string) => {
        switch (type) {
            case 'school':
                return 'text-blue-600 bg-blue-100';
            case 'hospital':
                return 'text-red-600 bg-red-100';
            case 'shopping':
                return 'text-green-600 bg-green-100';
            case 'transport':
                return 'text-purple-600 bg-purple-100';
            case 'office':
                return 'text-indigo-600 bg-indigo-100';
            default:
                return 'text-gray-600 bg-gray-100';
        }
    };

    const formatDistance = (distance: number) => {
        if (distance < 1) {
            return `${Math.round(distance * 1000)}m`;
        }
        return `${distance.toFixed(1)}km`;
    };

    // Mock data for demonstration
    const mockServices: NearbyService[] = [
        {
            id: '1',
            name: 'Ateneo de Manila University',
            type: 'school',
            address: 'Loyola Heights, Quezon City',
            distance: 2.3,
            rating: 4.8,
            reviews_count: 1250,
            description: 'Premier university known for excellence in education',
        },
        {
            id: '2',
            name: 'St. Luke\'s Medical Center',
            type: 'hospital',
            address: 'Global City, Taguig',
            distance: 1.8,
            rating: 4.6,
            reviews_count: 890,
            phone: '+63 2 789 7700',
            hours: '24/7',
            description: 'Leading medical facility with comprehensive healthcare services',
        },
        {
            id: '3',
            name: 'SM Megamall',
            type: 'shopping',
            address: 'Ortigas Center, Mandaluyong',
            distance: 3.2,
            rating: 4.4,
            reviews_count: 2100,
            hours: '10:00 AM - 10:00 PM',
            description: 'One of the largest shopping malls in the Philippines',
        },
        {
            id: '4',
            name: 'MRT Ortigas Station',
            type: 'transport',
            address: 'Ortigas Avenue, Pasig',
            distance: 0.8,
            rating: 3.9,
            reviews_count: 450,
            hours: '5:00 AM - 10:30 PM',
            description: 'Major MRT station connecting to Metro Manila',
        },
        {
            id: '5',
            name: 'Ortigas Center Business District',
            type: 'office',
            address: 'Ortigas Center, Pasig',
            distance: 1.2,
            description: 'Major business and financial district',
        },
    ];

    const displayServices = services.length > 0 ? filteredServices : mockServices.filter(service => 
        activeFilter === 'all' || service.type === activeFilter
    );

    return (
        <div className="services-container bg-white rounded-lg shadow-lg p-6">
            {/* Header */}
            <div className="mb-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Nearby Services</h3>
                <p className="text-gray-600 flex items-center">
                    <MapPinIcon className="h-4 w-4 mr-1" />
                    Services near {propertyAddress}
                </p>
            </div>

            {/* Filter Tabs */}
            <div className="flex flex-wrap gap-2 mb-6">
                {serviceTypes.map((type) => {
                    const Icon = type.icon;
                    return (
                        <button
                            key={type.key}
                            onClick={() => setActiveFilter(type.key)}
                            className={`flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                                activeFilter === type.key
                                    ? 'bg-blue-600 text-white shadow-md'
                                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                            }`}
                        >
                            <Icon className="h-4 w-4 mr-2" />
                            {type.label}
                        </button>
                    );
                })}
            </div>

            {/* Services Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {displayServices.map((service) => {
                    const ServiceIcon = getServiceIcon(service.type);
                    const colorClasses = getServiceColor(service.type);
                    
                    return (
                        <div key={service.id} className="service-card bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 hover:-translate-y-1">
                            <div className="flex items-start space-x-3">
                                {/* Service Icon */}
                                <div className={`flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center ${colorClasses}`}>
                                    <ServiceIcon className="h-5 w-5" />
                                </div>

                                {/* Service Info */}
                                <div className="flex-1 min-w-0">
                                    <div className="flex items-start justify-between">
                                        <h4 className="text-sm font-semibold text-gray-900 truncate">
                                            {service.name}
                                        </h4>
                                        <span className="text-sm font-medium text-blue-600 ml-2">
                                            {formatDistance(service.distance)}
                                        </span>
                                    </div>

                                    <p className="text-xs text-gray-600 mt-1 truncate">
                                        {service.address}
                                    </p>

                                    {service.description && (
                                        <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                                            {service.description}
                                        </p>
                                    )}

                                    {/* Rating */}
                                    {service.rating && (
                                        <div className="flex items-center mt-2">
                                            <div className="flex items-center">
                                                {[...Array(5)].map((_, i) => (
                                                    <StarIconSolid
                                                        key={i}
                                                        className={`h-3 w-3 ${
                                                            i < Math.floor(service.rating!)
                                                                ? 'text-yellow-400'
                                                                : 'text-gray-300'
                                                        }`}
                                                    />
                                                ))}
                                            </div>
                                            <span className="text-xs text-gray-600 ml-1">
                                                {service.rating.toFixed(1)} ({service.reviews_count})
                                            </span>
                                        </div>
                                    )}

                                    {/* Additional Info */}
                                    <div className="flex items-center space-x-3 mt-2">
                                        {service.hours && (
                                            <div className="flex items-center text-xs text-gray-500">
                                                <ClockIcon className="h-3 w-3 mr-1" />
                                                {service.hours}
                                            </div>
                                        )}
                                        {service.phone && (
                                            <div className="flex items-center text-xs text-gray-500">
                                                <PhoneIcon className="h-3 w-3 mr-1" />
                                                {service.phone}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    );
                })}
            </div>

            {/* Empty State */}
            {displayServices.length === 0 && (
                <div className="text-center py-8">
                    <MapPinIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-500">No services found in this category</p>
                </div>
            )}

            {/* View on Map Button */}
            <div className="mt-6 text-center">
                <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                    <MapPinIcon className="h-4 w-4 mr-2" />
                    View All on Map
                </button>
            </div>
        </div>
    );
};

export default NearbyServices;
