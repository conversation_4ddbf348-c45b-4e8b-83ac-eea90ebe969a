<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Property;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PropertyComparisonController extends Controller
{
    /**
     * Compare multiple properties
     */
    public function compare(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'property_ids' => 'required|array|min:2|max:4',
            'property_ids.*' => 'required|exists:properties,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $propertyIds = $request->property_ids;

        // Get properties with all necessary data
        $properties = Property::with(['user', 'primaryImage', 'images'])
            ->whereIn('id', $propertyIds)
            ->available()
            ->get();

        if ($properties->count() !== count($propertyIds)) {
            return response()->json([
                'message' => 'Some properties are not available or do not exist'
            ], 400);
        }

        // Calculate additional metrics for comparison
        $comparisonData = $properties->map(function ($property) {
            return [
                'id' => $property->id,
                'title' => $property->title,
                'type' => $property->type,
                'listing_type' => $property->listing_type,
                'price' => $property->price,
                'price_per_sqm' => $property->area > 0 ? round($property->price / $property->area, 2) : null,
                'bedrooms' => $property->bedrooms,
                'bathrooms' => $property->bathrooms,
                'area' => $property->area,
                'address' => $property->address,
                'city' => $property->city,
                'state' => $property->state,
                'amenities' => $property->amenities ?? [],
                'features' => $property->features ?? [],
                'amenities_count' => count($property->amenities ?? []),
                'features_count' => count($property->features ?? []),
                'is_featured' => $property->is_featured,
                'views' => $property->views,
                'average_rating' => $property->average_rating,
                'reviews_count' => $property->reviews()->count(),
                'primary_image' => $property->primaryImage,
                'images_count' => $property->images()->count(),
                'landlord' => [
                    'id' => $property->user->id,
                    'name' => $property->user->name,
                    'user_type' => $property->user->user_type,
                ],
                'created_at' => $property->created_at,
                'available_from' => $property->available_from,
            ];
        });

        // Generate comparison insights
        $insights = $this->generateComparisonInsights($comparisonData);

        return response()->json([
            'properties' => $comparisonData,
            'insights' => $insights,
            'comparison_date' => now()->toISOString(),
        ]);
    }

    /**
     * Get similar properties for comparison
     */
    public function getSimilarProperties(Request $request, string $propertyId)
    {
        $property = Property::findOrFail($propertyId);

        $validator = Validator::make($request->all(), [
            'limit' => 'sometimes|integer|min:1|max:10',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $limit = $request->get('limit', 5);

        // Find similar properties based on type, price range, and location
        $priceRange = $property->price * 0.3; // 30% price range
        $minPrice = $property->price - $priceRange;
        $maxPrice = $property->price + $priceRange;

        $similarProperties = Property::with(['user', 'primaryImage'])
            ->where('id', '!=', $property->id)
            ->where('type', $property->type)
            ->where('listing_type', $property->listing_type)
            ->whereBetween('price', [$minPrice, $maxPrice])
            ->where(function ($query) use ($property) {
                $query->where('city', $property->city)
                      ->orWhere('state', $property->state);
            })
            ->available()
            ->orderByRaw('ABS(price - ?) ASC', [$property->price])
            ->limit($limit)
            ->get();

        return response()->json([
            'base_property' => [
                'id' => $property->id,
                'title' => $property->title,
                'price' => $property->price,
                'type' => $property->type,
                'city' => $property->city,
            ],
            'similar_properties' => $similarProperties->map(function ($prop) {
                return [
                    'id' => $prop->id,
                    'title' => $prop->title,
                    'price' => $prop->price,
                    'type' => $prop->type,
                    'bedrooms' => $prop->bedrooms,
                    'bathrooms' => $prop->bathrooms,
                    'area' => $prop->area,
                    'city' => $prop->city,
                    'primary_image' => $prop->primaryImage,
                    'average_rating' => $prop->average_rating,
                ];
            }),
        ]);
    }

    /**
     * Generate comparison insights
     */
    private function generateComparisonInsights($properties)
    {
        $insights = [];

        // Price analysis
        $prices = $properties->pluck('price')->toArray();
        $insights['price'] = [
            'lowest' => min($prices),
            'highest' => max($prices),
            'average' => round(array_sum($prices) / count($prices), 2),
            'difference' => max($prices) - min($prices),
        ];

        // Area analysis
        $areas = $properties->pluck('area')->filter()->toArray();
        if (!empty($areas)) {
            $insights['area'] = [
                'smallest' => min($areas),
                'largest' => max($areas),
                'average' => round(array_sum($areas) / count($areas), 2),
            ];
        }

        // Price per sqm analysis
        $pricePerSqm = $properties->pluck('price_per_sqm')->filter()->toArray();
        if (!empty($pricePerSqm)) {
            $insights['price_per_sqm'] = [
                'lowest' => min($pricePerSqm),
                'highest' => max($pricePerSqm),
                'average' => round(array_sum($pricePerSqm) / count($pricePerSqm), 2),
            ];
        }

        // Bedrooms analysis
        $bedrooms = $properties->pluck('bedrooms')->toArray();
        $insights['bedrooms'] = [
            'min' => min($bedrooms),
            'max' => max($bedrooms),
            'average' => round(array_sum($bedrooms) / count($bedrooms), 1),
        ];

        // Amenities analysis
        $allAmenities = $properties->pluck('amenities')->flatten()->unique()->values();
        $insights['amenities'] = [
            'total_unique' => $allAmenities->count(),
            'common_amenities' => $this->findCommonAmenities($properties),
            'most_amenities' => $properties->max('amenities_count'),
            'least_amenities' => $properties->min('amenities_count'),
        ];

        // Rating analysis
        $ratings = $properties->pluck('average_rating')->filter()->toArray();
        if (!empty($ratings)) {
            $insights['ratings'] = [
                'highest' => max($ratings),
                'lowest' => min($ratings),
                'average' => round(array_sum($ratings) / count($ratings), 2),
            ];
        }

        // Best value analysis
        $insights['best_value'] = $this->findBestValue($properties);

        return $insights;
    }

    /**
     * Find common amenities across properties
     */
    private function findCommonAmenities($properties)
    {
        $amenitiesByProperty = $properties->pluck('amenities')->toArray();
        
        if (empty($amenitiesByProperty)) {
            return [];
        }

        $commonAmenities = array_shift($amenitiesByProperty);
        
        foreach ($amenitiesByProperty as $amenities) {
            $commonAmenities = array_intersect($commonAmenities, $amenities);
        }

        return array_values($commonAmenities);
    }

    /**
     * Find best value property
     */
    private function findBestValue($properties)
    {
        $bestValue = null;
        $bestScore = 0;

        foreach ($properties as $property) {
            // Calculate value score based on multiple factors
            $score = 0;
            
            // Price factor (lower is better)
            $priceScore = $property['price'] > 0 ? (1 / $property['price']) * 1000000 : 0;
            $score += $priceScore * 0.3;
            
            // Area factor (larger is better)
            $areaScore = $property['area'] ?? 0;
            $score += $areaScore * 0.2;
            
            // Amenities factor
            $amenitiesScore = $property['amenities_count'];
            $score += $amenitiesScore * 0.2;
            
            // Rating factor
            $ratingScore = $property['average_rating'] ?? 0;
            $score += $ratingScore * 0.3;

            if ($score > $bestScore) {
                $bestScore = $score;
                $bestValue = [
                    'property_id' => $property['id'],
                    'title' => $property['title'],
                    'score' => round($score, 2),
                ];
            }
        }

        return $bestValue;
    }
}
