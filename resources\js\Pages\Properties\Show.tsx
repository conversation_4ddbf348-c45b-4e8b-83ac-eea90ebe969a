import React, { useState, useEffect, useRef } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { PageProps } from '@/types';
import GuestLayout from '@/Layouts/GuestLayout';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import AvailabilityCalendar from '@/Components/AvailabilityCalendar';
import NearbyServices from '@/Components/NearbyServices';
import MessagingSystem from '@/Components/MessagingSystem';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import {
    HeartIcon,
    ShareIcon,
    MapPinIcon,
    HomeIcon,
    BanknotesIcon,
    CalendarIcon,
    EyeIcon,
    StarIcon,
    UserIcon,
    PhoneIcon,
    EnvelopeIcon,
    ChevronLeftIcon,
    ChevronRightIcon,
    XMarkIcon,
    PlayIcon,
    CameraIcon,
    ChatBubbleLeftRightIcon,
    ScaleIcon,
    ClockIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid, StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

interface PropertyImage {
    id: number;
    image_path: string;
    alt_text?: string;
    sort_order: number;
    is_primary: boolean;
    image_url: string;
}

interface User {
    id: number;
    name: string;
    email: string;
    phone?: string;
    avatar?: string;
    user_type: string;
    is_verified: boolean;
}

interface Review {
    id: number;
    rating: number;
    comment: string;
    created_at: string;
    user: User;
}

interface Property {
    id: number;
    title: string;
    description: string;
    type: string;
    listing_type: string;
    price: number;
    bedrooms: number;
    bathrooms: number;
    area?: number;
    address: string;
    city: string;
    state: string;
    postal_code?: string;
    country: string;
    latitude?: number;
    longitude?: number;
    amenities: string[];
    features: string[];
    is_featured: boolean;
    is_available: boolean;
    status: string;
    available_from?: string;
    video_url?: string;
    views: number;
    created_at: string;
    updated_at: string;
    formatted_price: string;
    average_rating: number;
    user: User;
    images: PropertyImage[];
    reviews: Review[];
    favorites_count?: number;
    is_favorited?: boolean;
}

interface ShowProps extends PageProps {
    property: Property;
}

export default function Show({ auth, property }: ShowProps) {
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [isGalleryOpen, setIsGalleryOpen] = useState(false);
    const [isFavorited, setIsFavorited] = useState(property.is_favorited || false);
    const [showContactForm, setShowContactForm] = useState(false);
    const [showMessaging, setShowMessaging] = useState(false);
    const [showCalendar, setShowCalendar] = useState(false);
    const [activeTab, setActiveTab] = useState('overview');

    // Refs for animations
    const heroRef = useRef<HTMLDivElement>(null);
    const detailsRef = useRef<HTMLDivElement>(null);
    const galleryRef = useRef<HTMLDivElement>(null);

    const Layout = auth.user ? AuthenticatedLayout : GuestLayout;

    useEffect(() => {
        gsap.registerPlugin(ScrollTrigger);

        // Hero image animation
        gsap.fromTo('.property-hero-image',
            { scale: 1.1, opacity: 0 },
            { scale: 1, opacity: 1, duration: 1.2, ease: "power3.out" }
        );

        // Property details animation
        gsap.fromTo('.property-detail-card',
            { y: 50, opacity: 0 },
            {
                y: 0,
                opacity: 1,
                duration: 0.8,
                stagger: 0.2,
                scrollTrigger: {
                    trigger: detailsRef.current,
                    start: "top 80%",
                    toggleActions: "play none none reverse"
                }
            }
        );

        // Gallery animation
        gsap.fromTo('.gallery-item',
            { scale: 0.8, opacity: 0 },
            {
                scale: 1,
                opacity: 1,
                duration: 0.6,
                stagger: 0.1,
                scrollTrigger: {
                    trigger: galleryRef.current,
                    start: "top 80%",
                    toggleActions: "play none none reverse"
                }
            }
        );

        return () => {
            ScrollTrigger.getAll().forEach(trigger => trigger.kill());
        };
    }, []);

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP',
            minimumFractionDigits: 0,
        }).format(price);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-PH', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const handlePreviousImage = () => {
        setCurrentImageIndex((prev) =>
            prev === 0 ? property.images.length - 1 : prev - 1
        );
    };

    const handleNextImage = () => {
        setCurrentImageIndex((prev) =>
            prev === property.images.length - 1 ? 0 : prev + 1
        );
    };

    const handleToggleFavorite = async () => {
        if (!auth.user) {
            router.visit('/login');
            return;
        }

        try {
            const response = await fetch('/api/favorites', {
                method: isFavorited ? 'DELETE' : 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    property_id: property.id
                })
            });

            if (response.ok) {
                setIsFavorited(!isFavorited);
            }
        } catch (error) {
            console.error('Error toggling favorite:', error);
        }
    };

    const handleShare = async () => {
        if (navigator.share) {
            try {
                await navigator.share({
                    title: property.title,
                    text: property.description,
                    url: window.location.href,
                });
            } catch (error) {
                console.log('Error sharing:', error);
            }
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(window.location.href);
            // You could show a toast notification here
        }
    };

    const currentImage = property.images[currentImageIndex];

    return (
        <Layout
            header={
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link
                            href="/properties"
                            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
                        >
                            <ChevronLeftIcon className="h-5 w-5 mr-1" />
                            Back to Properties
                        </Link>
                    </div>
                    <div className="flex items-center space-x-2">
                        <button
                            onClick={handleShare}
                            className="flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
                        >
                            <ShareIcon className="h-5 w-5 mr-1" />
                            Share
                        </button>
                        <button
                            onClick={handleToggleFavorite}
                            className="flex items-center px-3 py-2 text-gray-600 hover:text-red-500 transition-colors"
                        >
                            {isFavorited ? (
                                <HeartIconSolid className="h-5 w-5 mr-1 text-red-500" />
                            ) : (
                                <HeartIcon className="h-5 w-5 mr-1" />
                            )}
                            {isFavorited ? 'Saved' : 'Save'}
                        </button>
                    </div>
                </div>
            }
        >
            <Head title={`${property.title} - PropertyHub`} />

            <div className="min-h-screen bg-gray-50">
                {/* Hero Section */}
                <div ref={heroRef} className="relative h-96 md:h-[500px] overflow-hidden">
                    {currentImage && (
                        <img
                            src={currentImage.image_url}
                            alt={currentImage.alt_text || property.title}
                            className="property-hero-image w-full h-full object-cover"
                        />
                    )}

                    {/* Image Navigation */}
                    {property.images.length > 1 && (
                        <>
                            <button
                                onClick={handlePreviousImage}
                                className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
                            >
                                <ChevronLeftIcon className="h-6 w-6" />
                            </button>
                            <button
                                onClick={handleNextImage}
                                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
                            >
                                <ChevronRightIcon className="h-6 w-6" />
                            </button>
                        </>
                    )}

                    {/* Gallery Button */}
                    <button
                        onClick={() => setIsGalleryOpen(true)}
                        className="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white px-4 py-2 rounded-lg hover:bg-opacity-70 transition-all flex items-center"
                    >
                        <CameraIcon className="h-5 w-5 mr-2" />
                        View All Photos ({property.images.length})
                    </button>

                    {/* Property Status Badge */}
                    <div className="absolute top-4 left-4">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                            property.listing_type === 'rent'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-blue-100 text-blue-800'
                        }`}>
                            For {property.listing_type === 'rent' ? 'Rent' : 'Sale'}
                        </span>
                    </div>

                    {/* Featured Badge */}
                    {property.is_featured && (
                        <div className="absolute top-4 left-20">
                            <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                                Featured
                            </span>
                        </div>
                    )}
                </div>

                {/* Property Information */}
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* Main Content */}
                        <div className="lg:col-span-2 space-y-8" ref={detailsRef}>
                            {/* Property Header */}
                            <div className="property-detail-card bg-white rounded-lg shadow-md p-6">
                                <div className="flex items-start justify-between mb-4">
                                    <div>
                                        <h1 className="text-3xl font-bold text-gray-900 mb-2">{property.title}</h1>
                                        <div className="flex items-center text-gray-600 mb-2">
                                            <MapPinIcon className="h-5 w-5 mr-1" />
                                            <span>{property.address}, {property.city}, {property.state}</span>
                                        </div>
                                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                                            <div className="flex items-center">
                                                <EyeIcon className="h-4 w-4 mr-1" />
                                                <span>{property.views} views</span>
                                            </div>
                                            <div className="flex items-center">
                                                <CalendarIcon className="h-4 w-4 mr-1" />
                                                <span>Listed {formatDate(property.created_at)}</span>
                                            </div>
                                            {property.average_rating > 0 && (
                                                <div className="flex items-center">
                                                    <StarIconSolid className="h-4 w-4 mr-1 text-yellow-400" />
                                                    <span>{property.average_rating.toFixed(1)} ({property.reviews.length} reviews)</span>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div className="text-3xl font-bold text-blue-600 mb-1">
                                            {formatPrice(property.price)}
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {property.listing_type === 'rent' ? 'per month' : 'total price'}
                                        </div>
                                    </div>
                                </div>

                                {/* Property Features */}
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 py-4 border-t border-gray-200">
                                    <div className="text-center">
                                        <HomeIcon className="h-6 w-6 text-gray-400 mx-auto mb-1" />
                                        <div className="text-sm font-medium text-gray-900">{property.bedrooms}</div>
                                        <div className="text-xs text-gray-500">Bedrooms</div>
                                    </div>
                                    <div className="text-center">
                                        <div className="h-6 w-6 text-gray-400 mx-auto mb-1 flex items-center justify-center">
                                            🚿
                                        </div>
                                        <div className="text-sm font-medium text-gray-900">{property.bathrooms}</div>
                                        <div className="text-xs text-gray-500">Bathrooms</div>
                                    </div>
                                    {property.area && (
                                        <div className="text-center">
                                            <div className="h-6 w-6 text-gray-400 mx-auto mb-1 flex items-center justify-center">
                                                📐
                                            </div>
                                            <div className="text-sm font-medium text-gray-900">{property.area} m²</div>
                                            <div className="text-xs text-gray-500">Floor Area</div>
                                        </div>
                                    )}
                                    <div className="text-center">
                                        <div className="h-6 w-6 text-gray-400 mx-auto mb-1 flex items-center justify-center">
                                            🏠
                                        </div>
                                        <div className="text-sm font-medium text-gray-900 capitalize">{property.type}</div>
                                        <div className="text-xs text-gray-500">Property Type</div>
                                    </div>
                                </div>
                            </div>

                            {/* Description */}
                            <div className="property-detail-card bg-white rounded-lg shadow-md p-6">
                                <h2 className="text-xl font-semibold text-gray-900 mb-4">Description</h2>
                                <div className="prose prose-gray max-w-none">
                                    <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                                        {property.description}
                                    </p>
                                </div>
                            </div>

                            {/* Amenities & Features */}
                            {(property.amenities.length > 0 || property.features.length > 0) && (
                                <div className="property-detail-card bg-white rounded-lg shadow-md p-6">
                                    <h2 className="text-xl font-semibold text-gray-900 mb-4">Amenities & Features</h2>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        {property.amenities.length > 0 && (
                                            <div>
                                                <h3 className="text-lg font-medium text-gray-900 mb-3">Amenities</h3>
                                                <ul className="space-y-2">
                                                    {property.amenities.map((amenity, index) => (
                                                        <li key={index} className="flex items-center text-gray-700">
                                                            <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                                                            {amenity}
                                                        </li>
                                                    ))}
                                                </ul>
                                            </div>
                                        )}
                                        {property.features.length > 0 && (
                                            <div>
                                                <h3 className="text-lg font-medium text-gray-900 mb-3">Features</h3>
                                                <ul className="space-y-2">
                                                    {property.features.map((feature, index) => (
                                                        <li key={index} className="flex items-center text-gray-700">
                                                            <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                                                            {feature}
                                                        </li>
                                                    ))}
                                                </ul>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Video Tour */}
                            {property.video_url && (
                                <div className="property-detail-card bg-white rounded-lg shadow-md p-6">
                                    <h2 className="text-xl font-semibold text-gray-900 mb-4">Video Tour</h2>
                                    <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
                                        <iframe
                                            src={property.video_url}
                                            className="w-full h-full"
                                            allowFullScreen
                                            title="Property Video Tour"
                                        ></iframe>
                                    </div>
                                </div>
                            )}

                            {/* Photo Gallery */}
                            {property.images.length > 1 && (
                                <div className="property-detail-card bg-white rounded-lg shadow-md p-6" ref={galleryRef}>
                                    <h2 className="text-xl font-semibold text-gray-900 mb-4">Photo Gallery</h2>
                                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                        {property.images.slice(0, 8).map((image, index) => (
                                            <div
                                                key={image.id}
                                                className="gallery-item aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer hover:opacity-80 transition-opacity"
                                                onClick={() => {
                                                    setCurrentImageIndex(index);
                                                    setIsGalleryOpen(true);
                                                }}
                                            >
                                                <img
                                                    src={image.image_url}
                                                    alt={image.alt_text || `Property image ${index + 1}`}
                                                    className="w-full h-full object-cover"
                                                />
                                            </div>
                                        ))}
                                        {property.images.length > 8 && (
                                            <div
                                                className="gallery-item aspect-square bg-gray-900 bg-opacity-75 rounded-lg flex items-center justify-center cursor-pointer hover:bg-opacity-60 transition-all"
                                                onClick={() => setIsGalleryOpen(true)}
                                            >
                                                <div className="text-white text-center">
                                                    <CameraIcon className="h-8 w-8 mx-auto mb-2" />
                                                    <div className="text-sm font-medium">
                                                        +{property.images.length - 8} more
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Reviews Section */}
                            {property.reviews.length > 0 && (
                                <div className="property-detail-card bg-white rounded-lg shadow-md p-6">
                                    <h2 className="text-xl font-semibold text-gray-900 mb-4">
                                        Reviews ({property.reviews.length})
                                    </h2>
                                    <div className="space-y-6">
                                        {property.reviews.slice(0, 5).map((review) => (
                                            <div key={review.id} className="border-b border-gray-200 pb-4 last:border-b-0">
                                                <div className="flex items-start justify-between mb-2">
                                                    <div className="flex items-center">
                                                        <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                                                            <UserIcon className="h-6 w-6 text-gray-600" />
                                                        </div>
                                                        <div>
                                                            <div className="font-medium text-gray-900">{review.user.name}</div>
                                                            <div className="text-sm text-gray-500">{formatDate(review.created_at)}</div>
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center">
                                                        {[...Array(5)].map((_, i) => (
                                                            <StarIconSolid
                                                                key={i}
                                                                className={`h-4 w-4 ${
                                                                    i < review.rating ? 'text-yellow-400' : 'text-gray-300'
                                                                }`}
                                                            />
                                                        ))}
                                                    </div>
                                                </div>
                                                <p className="text-gray-700">{review.comment}</p>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Sidebar */}
                        <div className="lg:col-span-1">
                            <div className="sticky top-8 space-y-6">
                                {/* Contact Card */}
                                <div className="bg-white rounded-lg shadow-md p-6">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Property Owner</h3>

                                    {/* Owner Info */}
                                    <div className="flex items-center mb-4">
                                        <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                                            <UserIcon className="h-8 w-8 text-gray-600" />
                                        </div>
                                        <div>
                                            <div className="font-medium text-gray-900">{property.user.name}</div>
                                            <div className="text-sm text-gray-500 capitalize">{property.user.user_type}</div>
                                            {property.user.is_verified && (
                                                <div className="text-xs text-green-600 flex items-center">
                                                    <span className="w-2 h-2 bg-green-600 rounded-full mr-1"></span>
                                                    Verified
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    {/* Contact Buttons */}
                                    <div className="space-y-3">
                                        {property.user.phone && (
                                            <a
                                                href={`tel:${property.user.phone}`}
                                                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 hover:scale-105 active:scale-95 transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg"
                                            >
                                                <PhoneIcon className="h-5 w-5 mr-2" />
                                                Call Now
                                            </a>
                                        )}
                                        <button
                                            onClick={() => setShowMessaging(true)}
                                            className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 hover:scale-105 active:scale-95 transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg"
                                        >
                                            <ChatBubbleLeftRightIcon className="h-5 w-5 mr-2" />
                                            Chat Now
                                        </button>
                                        <button
                                            onClick={() => setShowContactForm(true)}
                                            className="w-full bg-gray-100 text-gray-900 py-3 px-4 rounded-lg hover:bg-gray-200 hover:scale-105 active:scale-95 transition-all duration-200 flex items-center justify-center"
                                        >
                                            <EnvelopeIcon className="h-5 w-5 mr-2" />
                                            Send Email
                                        </button>
                                        {auth.user && (
                                            <>
                                                <Link
                                                    href={`/properties/${property.id}/book`}
                                                    className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 hover:scale-105 active:scale-95 transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg"
                                                >
                                                    <CalendarIcon className="h-5 w-5 mr-2" />
                                                    Schedule Viewing
                                                </Link>
                                                {property.listing_type === 'rent' && (
                                                    <button
                                                        onClick={() => setShowCalendar(true)}
                                                        className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 hover:scale-105 active:scale-95 transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg"
                                                    >
                                                        <ClockIcon className="h-5 w-5 mr-2" />
                                                        Check Availability
                                                    </button>
                                                )}
                                            </>
                                        )}
                                    </div>
                                </div>

                                {/* Property Details Card */}
                                <div className="bg-white rounded-lg shadow-md p-6">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Property Details</h3>
                                    <div className="space-y-3">
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Property Type</span>
                                            <span className="font-medium capitalize">{property.type}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Listing Type</span>
                                            <span className="font-medium capitalize">For {property.listing_type}</span>
                                        </div>
                                        {property.area && (
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">Floor Area</span>
                                                <span className="font-medium">{property.area} m²</span>
                                            </div>
                                        )}
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Bedrooms</span>
                                            <span className="font-medium">{property.bedrooms}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Bathrooms</span>
                                            <span className="font-medium">{property.bathrooms}</span>
                                        </div>
                                        {property.available_from && (
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">Available From</span>
                                                <span className="font-medium">{formatDate(property.available_from)}</span>
                                            </div>
                                        )}
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Property ID</span>
                                            <span className="font-medium">#{property.id}</span>
                                        </div>
                                    </div>
                                </div>

                                {/* Map Placeholder */}
                                {(property.latitude && property.longitude) && (
                                    <div className="bg-white rounded-lg shadow-md p-6">
                                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Location</h3>
                                        <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                                            <div className="text-center text-gray-500">
                                                <MapPinIcon className="h-12 w-12 mx-auto mb-2" />
                                                <p>Map integration coming soon</p>
                                                <p className="text-sm">{property.address}</p>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Enhanced Sections */}
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    {/* Tab Navigation */}
                    <div className="border-b border-gray-200 mb-8">
                        <nav className="-mb-px flex space-x-8">
                            {[
                                { key: 'overview', label: 'Overview', icon: HomeIcon },
                                { key: 'availability', label: 'Availability', icon: CalendarIcon },
                                { key: 'nearby', label: 'Nearby Services', icon: MapPinIcon },
                                { key: 'compare', label: 'Compare', icon: ScaleIcon },
                            ].map((tab) => {
                                const Icon = tab.icon;
                                return (
                                    <button
                                        key={tab.key}
                                        onClick={() => setActiveTab(tab.key)}
                                        className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                                            activeTab === tab.key
                                                ? 'border-blue-500 text-blue-600'
                                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                        }`}
                                    >
                                        <Icon className="h-5 w-5 mr-2" />
                                        {tab.label}
                                    </button>
                                );
                            })}
                        </nav>
                    </div>

                    {/* Tab Content */}
                    <div className="tab-content">
                        {activeTab === 'overview' && (
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-4">Property Highlights</h3>
                                    <div className="bg-white rounded-lg shadow-md p-6">
                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="text-center p-4 bg-blue-50 rounded-lg">
                                                <div className="text-2xl font-bold text-blue-600">{property.bedrooms}</div>
                                                <div className="text-sm text-gray-600">Bedrooms</div>
                                            </div>
                                            <div className="text-center p-4 bg-green-50 rounded-lg">
                                                <div className="text-2xl font-bold text-green-600">{property.bathrooms}</div>
                                                <div className="text-sm text-gray-600">Bathrooms</div>
                                            </div>
                                            {property.area && (
                                                <div className="text-center p-4 bg-purple-50 rounded-lg">
                                                    <div className="text-2xl font-bold text-purple-600">{property.area}</div>
                                                    <div className="text-sm text-gray-600">m² Area</div>
                                                </div>
                                            )}
                                            <div className="text-center p-4 bg-yellow-50 rounded-lg">
                                                <div className="text-2xl font-bold text-yellow-600">{property.views}</div>
                                                <div className="text-sm text-gray-600">Views</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h3>
                                    <div className="bg-white rounded-lg shadow-md p-6 space-y-4">
                                        <button
                                            onClick={() => setShowMessaging(true)}
                                            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                                        >
                                            <ChatBubbleLeftRightIcon className="h-5 w-5 mr-2" />
                                            Start Conversation
                                        </button>
                                        <button className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center">
                                            <ScaleIcon className="h-5 w-5 mr-2" />
                                            Add to Compare
                                        </button>
                                        <button className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors flex items-center justify-center">
                                            <ShareIcon className="h-5 w-5 mr-2" />
                                            Share Property
                                        </button>
                                    </div>
                                </div>
                            </div>
                        )}

                        {activeTab === 'availability' && property.listing_type === 'rent' && (
                            <AvailabilityCalendar
                                propertyId={property.id}
                                basePrice={property.price}
                                listingType={property.listing_type}
                                allowRangeSelection={true}
                                onDateRangeSelect={(start, end) => {
                                    console.log('Selected range:', start, end);
                                }}
                            />
                        )}

                        {activeTab === 'nearby' && (
                            <NearbyServices
                                propertyLatitude={property.latitude || 14.5995}
                                propertyLongitude={property.longitude || 120.9842}
                                propertyAddress={property.address}
                            />
                        )}

                        {activeTab === 'compare' && (
                            <div className="bg-white rounded-lg shadow-md p-6">
                                <h3 className="text-xl font-semibold text-gray-900 mb-4">Property Comparison</h3>
                                <p className="text-gray-600 mb-6">
                                    Add this property to your comparison list to compare it with other properties side by side.
                                </p>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                        <ScaleIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                                        <h4 className="font-medium text-gray-900 mb-2">Compare Properties</h4>
                                        <p className="text-sm text-gray-600 mb-4">
                                            Select up to 4 properties to compare features, prices, and amenities
                                        </p>
                                        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                            Add to Compare
                                        </button>
                                    </div>
                                    <div className="bg-gray-50 rounded-lg p-6">
                                        <h4 className="font-medium text-gray-900 mb-3">Comparison Features</h4>
                                        <ul className="space-y-2 text-sm text-gray-600">
                                            <li className="flex items-center">
                                                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                                                Side-by-side property details
                                            </li>
                                            <li className="flex items-center">
                                                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                                                Amenities and features comparison
                                            </li>
                                            <li className="flex items-center">
                                                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                                                Price and value analysis
                                            </li>
                                            <li className="flex items-center">
                                                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                                                Location and accessibility
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Full-Screen Gallery Modal */}
                {isGalleryOpen && (
                    <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center">
                        <div className="relative w-full h-full flex items-center justify-center">
                            {/* Close Button */}
                            <button
                                onClick={() => setIsGalleryOpen(false)}
                                className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
                            >
                                <XMarkIcon className="h-8 w-8" />
                            </button>

                            {/* Image Navigation */}
                            {property.images.length > 1 && (
                                <>
                                    <button
                                        onClick={handlePreviousImage}
                                        className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-10"
                                    >
                                        <ChevronLeftIcon className="h-12 w-12" />
                                    </button>
                                    <button
                                        onClick={handleNextImage}
                                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-10"
                                    >
                                        <ChevronRightIcon className="h-12 w-12" />
                                    </button>
                                </>
                            )}

                            {/* Current Image */}
                            {currentImage && (
                                <div className="max-w-4xl max-h-full p-4">
                                    <img
                                        src={currentImage.image_url}
                                        alt={currentImage.alt_text || property.title}
                                        className="max-w-full max-h-full object-contain"
                                    />
                                </div>
                            )}

                            {/* Image Counter */}
                            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white">
                                <span>{currentImageIndex + 1} of {property.images.length}</span>
                            </div>

                            {/* Thumbnail Strip */}
                            <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 flex space-x-2 max-w-full overflow-x-auto">
                                {property.images.map((image, index) => (
                                    <button
                                        key={image.id}
                                        onClick={() => setCurrentImageIndex(index)}
                                        className={`w-16 h-16 rounded-lg overflow-hidden flex-shrink-0 ${
                                            index === currentImageIndex ? 'ring-2 ring-white' : 'opacity-60 hover:opacity-80'
                                        }`}
                                    >
                                        <img
                                            src={image.image_url}
                                            alt={image.alt_text || `Thumbnail ${index + 1}`}
                                            className="w-full h-full object-cover"
                                        />
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>
                )}

                {/* Contact Form Modal */}
                {showContactForm && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                        <div className="bg-white rounded-lg max-w-md w-full p-6">
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-lg font-semibold text-gray-900">Send Message</h3>
                                <button
                                    onClick={() => setShowContactForm(false)}
                                    className="text-gray-400 hover:text-gray-600"
                                >
                                    <XMarkIcon className="h-6 w-6" />
                                </button>
                            </div>
                            <form className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Your Name
                                    </label>
                                    <input
                                        type="text"
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter your name"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Email
                                    </label>
                                    <input
                                        type="email"
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter your email"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Phone (Optional)
                                    </label>
                                    <input
                                        type="tel"
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter your phone number"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Message
                                    </label>
                                    <textarea
                                        rows={4}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="I'm interested in this property..."
                                    ></textarea>
                                </div>
                                <div className="flex space-x-3">
                                    <button
                                        type="button"
                                        onClick={() => setShowContactForm(false)}
                                        className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        type="submit"
                                        className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                                    >
                                        Send Message
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                )}

                {/* Messaging Modal */}
                {showMessaging && auth.user && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                        <MessagingSystem
                            currentUser={auth.user as any}
                            conversations={[
                                {
                                    id: 1,
                                    property_id: property.id,
                                    property_title: property.title,
                                    participants: [auth.user as any, property.user as any],
                                    unread_count: 0,
                                    updated_at: new Date().toISOString(),
                                }
                            ]}
                            activeConversationId={1}
                            onSendMessage={(conversationId, content) => {
                                console.log('Send message:', conversationId, content);
                            }}
                            onMarkAsRead={(conversationId) => {
                                console.log('Mark as read:', conversationId);
                            }}
                            onClose={() => setShowMessaging(false)}
                            isModal={true}
                        />
                    </div>
                )}

                {/* Calendar Modal */}
                {showCalendar && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                        <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
                            <div className="p-6">
                                <div className="flex items-center justify-between mb-6">
                                    <h3 className="text-xl font-semibold text-gray-900">Property Availability</h3>
                                    <button
                                        onClick={() => setShowCalendar(false)}
                                        className="text-gray-400 hover:text-gray-600"
                                    >
                                        <XMarkIcon className="h-6 w-6" />
                                    </button>
                                </div>
                                <AvailabilityCalendar
                                    propertyId={property.id}
                                    basePrice={property.price}
                                    listingType={property.listing_type as 'rent' | 'sale'}
                                    allowRangeSelection={true}
                                    onDateRangeSelect={(start, end) => {
                                        console.log('Selected range:', start, end);
                                        // Here you could calculate total cost and show booking form
                                    }}
                                />
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </Layout>
    );
}
