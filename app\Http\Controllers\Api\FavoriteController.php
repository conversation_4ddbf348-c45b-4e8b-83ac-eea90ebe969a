<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Favorite;
use App\Models\Property;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class FavoriteController extends Controller
{
    /**
     * Display user's favorite properties
     */
    public function index()
    {
        $favorites = Favorite::with(['property.primaryImage', 'property.user'])
            ->where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json($favorites);
    }

    /**
     * Add property to favorites
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'property_id' => 'required|exists:properties,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $property = Property::findOrFail($request->property_id);

        // Check if user is not the property owner
        if ($property->user_id === Auth::id()) {
            return response()->json([
                'message' => 'You cannot favorite your own property'
            ], 400);
        }

        // Check if already favorited
        $existingFavorite = Favorite::where('property_id', $request->property_id)
            ->where('user_id', Auth::id())
            ->first();

        if ($existingFavorite) {
            return response()->json([
                'message' => 'Property is already in your favorites'
            ], 400);
        }

        $favorite = Favorite::create([
            'property_id' => $request->property_id,
            'user_id' => Auth::id(),
        ]);

        $favorite->load(['property.primaryImage']);

        return response()->json([
            'message' => 'Property added to favorites',
            'favorite' => $favorite
        ], 201);
    }

    /**
     * Check if property is favorited by user
     */
    public function show(string $propertyId)
    {
        $favorite = Favorite::where('property_id', $propertyId)
            ->where('user_id', Auth::id())
            ->first();

        return response()->json([
            'is_favorited' => $favorite !== null,
            'favorite_id' => $favorite ? $favorite->id : null
        ]);
    }

    /**
     * Remove property from favorites
     */
    public function destroy(string $id)
    {
        // $id can be either favorite ID or property ID
        $favorite = Favorite::where(function ($query) use ($id) {
            $query->where('id', $id)
                  ->orWhere('property_id', $id);
        })
        ->where('user_id', Auth::id())
        ->first();

        if (!$favorite) {
            return response()->json(['message' => 'Favorite not found'], 404);
        }

        $favorite->delete();

        return response()->json(['message' => 'Property removed from favorites']);
    }

    /**
     * Toggle favorite status
     */
    public function toggle(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'property_id' => 'required|exists:properties,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $property = Property::findOrFail($request->property_id);

        // Check if user is not the property owner
        if ($property->user_id === Auth::id()) {
            return response()->json([
                'message' => 'You cannot favorite your own property'
            ], 400);
        }

        $favorite = Favorite::where('property_id', $request->property_id)
            ->where('user_id', Auth::id())
            ->first();

        if ($favorite) {
            // Remove from favorites
            $favorite->delete();
            return response()->json([
                'message' => 'Property removed from favorites',
                'is_favorited' => false
            ]);
        } else {
            // Add to favorites
            $favorite = Favorite::create([
                'property_id' => $request->property_id,
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'message' => 'Property added to favorites',
                'is_favorited' => true,
                'favorite_id' => $favorite->id
            ]);
        }
    }
}
