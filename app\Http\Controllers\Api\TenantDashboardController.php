<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Payment;
use App\Models\Message;
use App\Models\Favorite;
use App\Models\Review;
use App\Models\Property;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TenantDashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            $user = Auth::user();
            if (!$user->isTenant()) {
                return response()->json(['message' => 'Unauthorized'], 403);
            }
            return $next($request);
        });
    }

    /**
     * Get tenant dashboard statistics
     */
    public function stats()
    {
        $userId = Auth::id();

        $stats = [
            'total_bookings' => Booking::where('user_id', $userId)->count(),
            'pending_bookings' => Booking::where('user_id', $userId)
                ->where('status', 'pending')->count(),
            'confirmed_bookings' => Booking::where('user_id', $userId)
                ->where('status', 'confirmed')->count(),
            'total_payments' => Payment::where('user_id', $userId)->count(),
            'total_spent' => Payment::where('user_id', $userId)
                ->where('status', 'paid')->sum('amount'),
            'pending_payments' => Payment::where('user_id', $userId)
                ->where('status', 'pending')->count(),
            'favorite_properties' => Favorite::where('user_id', $userId)->count(),
            'unread_messages' => Message::where('receiver_id', $userId)
                ->where('is_read', false)->count(),
            'reviews_given' => Review::where('user_id', $userId)->count(),
        ];

        // Recent search preferences (if stored)
        $recentSearches = []; // This could be implemented with a searches table

        // Spending by month (last 6 months)
        $monthlySpending = Payment::select(
            DB::raw('YEAR(paid_at) as year'),
            DB::raw('MONTH(paid_at) as month'),
            DB::raw('SUM(amount) as total')
        )
        ->where('user_id', $userId)
        ->where('status', 'paid')
        ->where('paid_at', '>=', now()->subMonths(6))
        ->groupBy('year', 'month')
        ->orderBy('year', 'asc')
        ->orderBy('month', 'asc')
        ->get();

        return response()->json([
            'stats' => $stats,
            'recent_searches' => $recentSearches,
            'monthly_spending' => $monthlySpending,
        ]);
    }

    /**
     * Get tenant's bookings
     */
    public function bookings(Request $request)
    {
        $query = Booking::with(['property.primaryImage', 'property.user'])
            ->where('user_id', Auth::id());

        // Status filter
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Type filter
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        $bookings = $query->orderBy('created_at', 'desc')->paginate(10);

        return response()->json($bookings);
    }

    /**
     * Get tenant's payments
     */
    public function payments(Request $request)
    {
        $query = Payment::with(['property'])
            ->where('user_id', Auth::id());

        // Status filter
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Payment type filter
        if ($request->has('payment_type')) {
            $query->where('payment_type', $request->payment_type);
        }

        $payments = $query->orderBy('created_at', 'desc')->paginate(10);

        return response()->json($payments);
    }

    /**
     * Get tenant's favorite properties
     */
    public function favorites(Request $request)
    {
        $query = Favorite::with(['property.primaryImage', 'property.user'])
            ->where('user_id', Auth::id());

        // Property type filter
        if ($request->has('property_type')) {
            $query->whereHas('property', function ($q) use ($request) {
                $q->where('type', $request->property_type);
            });
        }

        // Listing type filter
        if ($request->has('listing_type')) {
            $query->whereHas('property', function ($q) use ($request) {
                $q->where('listing_type', $request->listing_type);
            });
        }

        $favorites = $query->orderBy('created_at', 'desc')->paginate(10);

        return response()->json($favorites);
    }

    /**
     * Get tenant's messages/conversations
     */
    public function messages()
    {
        $userId = Auth::id();
        
        // Get conversations where user is sender or receiver
        $conversations = Message::select([
            DB::raw('CASE 
                WHEN sender_id = ' . $userId . ' THEN receiver_id 
                ELSE sender_id 
            END as other_user_id'),
            'property_id',
            DB::raw('MAX(created_at) as last_message_at'),
            DB::raw('COUNT(CASE WHEN receiver_id = ' . $userId . ' AND is_read = 0 THEN 1 END) as unread_count')
        ])
        ->where(function ($query) use ($userId) {
            $query->where('sender_id', $userId)
                  ->orWhere('receiver_id', $userId);
        })
        ->groupBy('other_user_id', 'property_id')
        ->orderBy('last_message_at', 'desc')
        ->get();

        // Load related data
        $conversations->load([
            'property:id,title,address',
            'property.primaryImage'
        ]);

        return response()->json($conversations);
    }

    /**
     * Get tenant's reviews
     */
    public function reviews(Request $request)
    {
        $query = Review::with(['property'])
            ->where('user_id', Auth::id());

        // Status filter
        if ($request->has('status')) {
            if ($request->status === 'pending') {
                $query->where('is_approved', false);
            } elseif ($request->status === 'approved') {
                $query->where('is_approved', true);
            }
        }

        $reviews = $query->orderBy('created_at', 'desc')->paginate(10);

        return response()->json($reviews);
    }

    /**
     * Get property recommendations for tenant
     */
    public function recommendations()
    {
        $userId = Auth::id();
        
        // Get user's favorite property types and price ranges
        $favoriteTypes = Favorite::join('properties', 'favorites.property_id', '=', 'properties.id')
            ->where('favorites.user_id', $userId)
            ->pluck('properties.type')
            ->unique()
            ->toArray();

        $averagePrice = Favorite::join('properties', 'favorites.property_id', '=', 'properties.id')
            ->where('favorites.user_id', $userId)
            ->avg('properties.price');

        // Get user's preferred locations
        $preferredCities = Favorite::join('properties', 'favorites.property_id', '=', 'properties.id')
            ->where('favorites.user_id', $userId)
            ->pluck('properties.city')
            ->unique()
            ->toArray();

        // Build recommendation query
        $query = Property::with(['primaryImage', 'user'])
            ->available()
            ->whereNotIn('id', function ($q) use ($userId) {
                $q->select('property_id')
                  ->from('favorites')
                  ->where('user_id', $userId);
            });

        // Apply filters based on preferences
        if (!empty($favoriteTypes)) {
            $query->whereIn('type', $favoriteTypes);
        }

        if ($averagePrice) {
            $priceRange = $averagePrice * 0.3; // 30% range
            $query->whereBetween('price', [
                $averagePrice - $priceRange,
                $averagePrice + $priceRange
            ]);
        }

        if (!empty($preferredCities)) {
            $query->whereIn('city', $preferredCities);
        }

        $recommendations = $query->inRandomOrder()->limit(6)->get();

        return response()->json($recommendations);
    }

    /**
     * Get recent activities
     */
    public function recentActivities()
    {
        $userId = Auth::id();
        $activities = [];

        // Recent bookings
        $recentBookings = Booking::with(['property'])
            ->where('user_id', $userId)
            ->latest()->take(5)->get();

        foreach ($recentBookings as $booking) {
            $activities[] = [
                'type' => 'booking',
                'description' => "You booked a {$booking->type} for {$booking->property->title}",
                'created_at' => $booking->created_at,
                'data' => $booking
            ];
        }

        // Recent payments
        $recentPayments = Payment::with(['property'])
            ->where('user_id', $userId)
            ->latest()->take(5)->get();

        foreach ($recentPayments as $payment) {
            $activities[] = [
                'type' => 'payment',
                'description' => "Payment of ₱{$payment->amount} for {$payment->property->title}",
                'created_at' => $payment->created_at,
                'data' => $payment
            ];
        }

        // Recent favorites
        $recentFavorites = Favorite::with(['property'])
            ->where('user_id', $userId)
            ->latest()->take(5)->get();

        foreach ($recentFavorites as $favorite) {
            $activities[] = [
                'type' => 'favorite',
                'description' => "You favorited {$favorite->property->title}",
                'created_at' => $favorite->created_at,
                'data' => $favorite
            ];
        }

        // Sort by created_at desc
        usort($activities, function ($a, $b) {
            return $b['created_at'] <=> $a['created_at'];
        });

        return response()->json(array_slice($activities, 0, 15));
    }
}
