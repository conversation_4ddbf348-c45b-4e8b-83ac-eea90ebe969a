<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Payment Receipt</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .receipt-title {
            font-size: 18px;
            color: #666;
        }
        .receipt-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .receipt-number {
            font-weight: bold;
            font-size: 16px;
        }
        .receipt-date {
            color: #666;
        }
        .section {
            margin-bottom: 25px;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #007bff;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }
        .info-label {
            font-weight: bold;
            width: 40%;
        }
        .info-value {
            width: 60%;
            text-align: right;
        }
        .amount-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .total-amount {
            font-size: 20px;
            font-weight: bold;
            color: #007bff;
            text-align: center;
            margin-top: 10px;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-paid {
            background-color: #d4edda;
            color: #155724;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-failed {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">Property Listing Platform</div>
        <div class="receipt-title">Payment Receipt</div>
    </div>

    <div class="receipt-info">
        <div>
            <div class="receipt-number">Receipt #{{ $payment->id }}</div>
            <div class="receipt-date">{{ $payment->created_at->format('F d, Y') }}</div>
        </div>
        <div>
            <span class="status-badge status-{{ $payment->status }}">
                {{ ucfirst($payment->status) }}
            </span>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Payment Information</div>
        <div class="info-row">
            <span class="info-label">Payment Type:</span>
            <span class="info-value">{{ ucfirst(str_replace('_', ' ', $payment->payment_type)) }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Payment Method:</span>
            <span class="info-value">{{ ucfirst($payment->payment_method) }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Transaction ID:</span>
            <span class="info-value">{{ $payment->transaction_id ?? 'N/A' }}</span>
        </div>
        @if($payment->paid_at)
        <div class="info-row">
            <span class="info-label">Payment Date:</span>
            <span class="info-value">{{ $payment->paid_at->format('F d, Y g:i A') }}</span>
        </div>
        @endif
    </div>

    <div class="section">
        <div class="section-title">Property Information</div>
        <div class="info-row">
            <span class="info-label">Property:</span>
            <span class="info-value">{{ $payment->property->title }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Address:</span>
            <span class="info-value">{{ $payment->property->address }}, {{ $payment->property->city }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Property Type:</span>
            <span class="info-value">{{ ucfirst($payment->property->type) }}</span>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Customer Information</div>
        <div class="info-row">
            <span class="info-label">Name:</span>
            <span class="info-value">{{ $payment->user->name }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Email:</span>
            <span class="info-value">{{ $payment->user->email }}</span>
        </div>
        @if($payment->user->phone)
        <div class="info-row">
            <span class="info-label">Phone:</span>
            <span class="info-value">{{ $payment->user->phone }}</span>
        </div>
        @endif
    </div>

    <div class="amount-section">
        <div class="info-row">
            <span class="info-label">Amount:</span>
            <span class="info-value">₱{{ number_format($payment->amount, 2) }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Currency:</span>
            <span class="info-value">{{ $payment->currency }}</span>
        </div>
        <div class="total-amount">
            Total: ₱{{ number_format($payment->amount, 2) }}
        </div>
    </div>

    @if($payment->notes)
    <div class="section">
        <div class="section-title">Notes</div>
        <p>{{ $payment->notes }}</p>
    </div>
    @endif

    <div class="footer">
        <p>This is a computer-generated receipt. No signature required.</p>
        <p>For any inquiries, please contact our support team.</p>
        <p>Generated on {{ now()->format('F d, Y g:i A') }}</p>
    </div>
</body>
</html>
