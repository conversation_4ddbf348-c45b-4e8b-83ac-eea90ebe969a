<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Property;
use App\Models\PropertyAvailability;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class AvailabilityController extends Controller
{
    /**
     * Get property availability calendar
     */
    public function getCalendar(Request $request, string $propertyId)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'sometimes|date|after_or_equal:today',
            'end_date' => 'sometimes|date|after:start_date',
            'month' => 'sometimes|integer|min:1|max:12',
            'year' => 'sometimes|integer|min:' . date('Y'),
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $property = Property::findOrFail($propertyId);

        // Determine date range
        if ($request->has('start_date') && $request->has('end_date')) {
            $startDate = $request->start_date;
            $endDate = $request->end_date;
        } elseif ($request->has('month') && $request->has('year')) {
            $startDate = Carbon::create($request->year, $request->month, 1)->format('Y-m-d');
            $endDate = Carbon::create($request->year, $request->month, 1)->endOfMonth()->format('Y-m-d');
        } else {
            // Default to current month
            $startDate = now()->startOfMonth()->format('Y-m-d');
            $endDate = now()->endOfMonth()->format('Y-m-d');
        }

        $calendar = PropertyAvailability::generateCalendar($propertyId, $startDate, $endDate);

        return response()->json([
            'property_id' => $propertyId,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'calendar' => $calendar,
            'property' => [
                'id' => $property->id,
                'title' => $property->title,
                'base_price' => $property->price,
                'listing_type' => $property->listing_type,
            ]
        ]);
    }

    /**
     * Update property availability (for property owners)
     */
    public function updateAvailability(Request $request, string $propertyId)
    {
        $property = Property::findOrFail($propertyId);

        // Check authorization
        if ($property->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'dates' => 'required|array|min:1',
            'dates.*.date' => 'required|date|after_or_equal:today',
            'dates.*.status' => 'required|in:available,booked,blocked,maintenance',
            'dates.*.price_override' => 'nullable|numeric|min:0',
            'dates.*.notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $updatedDates = [];

        foreach ($request->dates as $dateData) {
            $availability = PropertyAvailability::updateOrCreate(
                [
                    'property_id' => $propertyId,
                    'date' => $dateData['date']
                ],
                [
                    'status' => $dateData['status'],
                    'price_override' => $dateData['price_override'] ?? null,
                    'notes' => $dateData['notes'] ?? null,
                ]
            );

            $updatedDates[] = $availability;
        }

        return response()->json([
            'message' => 'Availability updated successfully',
            'updated_dates' => $updatedDates
        ]);
    }

    /**
     * Bulk update availability for date range
     */
    public function bulkUpdateAvailability(Request $request, string $propertyId)
    {
        $property = Property::findOrFail($propertyId);

        // Check authorization
        if ($property->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
            'status' => 'required|in:available,booked,blocked,maintenance',
            'price_override' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:500',
            'skip_weekends' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);
        $skipWeekends = $request->get('skip_weekends', false);

        $updatedDates = [];
        $current = $startDate->copy();

        while ($current <= $endDate) {
            // Skip weekends if requested
            if ($skipWeekends && $current->isWeekend()) {
                $current->addDay();
                continue;
            }

            $availability = PropertyAvailability::updateOrCreate(
                [
                    'property_id' => $propertyId,
                    'date' => $current->format('Y-m-d')
                ],
                [
                    'status' => $request->status,
                    'price_override' => $request->price_override,
                    'notes' => $request->notes,
                ]
            );

            $updatedDates[] = $availability;
            $current->addDay();
        }

        return response()->json([
            'message' => 'Bulk availability updated successfully',
            'updated_count' => count($updatedDates),
            'date_range' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d')
            ]
        ]);
    }

    /**
     * Check availability for specific dates
     */
    public function checkAvailability(Request $request, string $propertyId)
    {
        $validator = Validator::make($request->all(), [
            'dates' => 'required|array|min:1',
            'dates.*' => 'required|date|after_or_equal:today',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $property = Property::findOrFail($propertyId);
        $dates = $request->dates;
        $availability = [];

        foreach ($dates as $date) {
            $dateAvailability = PropertyAvailability::where('property_id', $propertyId)
                ->where('date', $date)
                ->first();

            $availability[] = [
                'date' => $date,
                'is_available' => $dateAvailability ? $dateAvailability->isAvailable() : true,
                'status' => $dateAvailability ? $dateAvailability->status : 'available',
                'price' => $dateAvailability ? $dateAvailability->getEffectivePrice() : $property->price,
                'notes' => $dateAvailability ? $dateAvailability->notes : null,
            ];
        }

        return response()->json([
            'property_id' => $propertyId,
            'availability' => $availability,
            'all_available' => collect($availability)->every(fn($item) => $item['is_available']),
        ]);
    }

    /**
     * Get available date ranges
     */
    public function getAvailableRanges(Request $request, string $propertyId)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'sometimes|date|after_or_equal:today',
            'end_date' => 'sometimes|date|after:start_date',
            'min_days' => 'sometimes|integer|min:1|max:365',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $property = Property::findOrFail($propertyId);
        $startDate = $request->get('start_date', now()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->addMonths(3)->format('Y-m-d'));
        $minDays = $request->get('min_days', 1);

        $calendar = PropertyAvailability::generateCalendar($propertyId, $startDate, $endDate);
        $availableRanges = [];
        $currentRange = null;

        foreach ($calendar as $day) {
            if ($day['status'] === 'available' && !$day['is_past']) {
                if (!$currentRange) {
                    $currentRange = [
                        'start_date' => $day['date'],
                        'end_date' => $day['date'],
                        'days' => 1,
                        'total_price' => $day['price'],
                    ];
                } else {
                    $currentRange['end_date'] = $day['date'];
                    $currentRange['days']++;
                    $currentRange['total_price'] += $day['price'];
                }
            } else {
                if ($currentRange && $currentRange['days'] >= $minDays) {
                    $availableRanges[] = $currentRange;
                }
                $currentRange = null;
            }
        }

        // Add the last range if it meets criteria
        if ($currentRange && $currentRange['days'] >= $minDays) {
            $availableRanges[] = $currentRange;
        }

        return response()->json([
            'property_id' => $propertyId,
            'search_criteria' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'min_days' => $minDays,
            ],
            'available_ranges' => $availableRanges,
            'total_ranges' => count($availableRanges),
        ]);
    }
}
