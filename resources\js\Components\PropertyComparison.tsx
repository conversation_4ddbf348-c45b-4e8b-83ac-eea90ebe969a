import React, { useState, useEffect } from 'react';
import { XMarkIcon, HomeIcon, MapPinIcon, BanknotesIcon, StarIcon } from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { gsap } from 'gsap';

interface Property {
    id: number;
    title: string;
    description: string;
    type: string;
    listing_type: string;
    price: number;
    bedrooms: number;
    bathrooms: number;
    area?: number;
    address: string;
    city: string;
    state: string;
    primary_image?: {
        image_path: string;
        alt_text?: string;
    };
    user: {
        name: string;
    };
    is_featured: boolean;
    amenities: string[];
    features: string[];
    average_rating?: number;
    reviews_count?: number;
    created_at: string;
}

interface PropertyComparisonProps {
    properties: Property[];
    onRemoveProperty: (propertyId: number) => void;
    onClose: () => void;
}

const PropertyComparison: React.FC<PropertyComparisonProps> = ({
    properties,
    onRemoveProperty,
    onClose,
}) => {
    useEffect(() => {
        // Animate comparison modal
        gsap.fromTo('.comparison-modal',
            { scale: 0.9, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.3, ease: "power2.out" }
        );

        gsap.fromTo('.comparison-card',
            { y: 30, opacity: 0 },
            { y: 0, opacity: 1, duration: 0.5, stagger: 0.1, delay: 0.2, ease: "power2.out" }
        );
    }, []);

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP',
            minimumFractionDigits: 0,
        }).format(price);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-PH', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    const getAllAmenities = () => {
        const allAmenities = new Set<string>();
        properties.forEach(property => {
            property.amenities.forEach(amenity => allAmenities.add(amenity));
        });
        return Array.from(allAmenities);
    };

    const getAllFeatures = () => {
        const allFeatures = new Set<string>();
        properties.forEach(property => {
            property.features.forEach(feature => allFeatures.add(feature));
        });
        return Array.from(allFeatures);
    };

    const hasAmenity = (property: Property, amenity: string) => {
        return property.amenities.includes(amenity);
    };

    const hasFeature = (property: Property, feature: string) => {
        return property.features.includes(feature);
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="comparison-modal bg-white rounded-lg max-w-7xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
                {/* Header */}
                <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6">
                    <div className="flex items-center justify-between">
                        <h2 className="text-2xl font-bold">Property Comparison</h2>
                        <button
                            onClick={onClose}
                            className="p-2 hover:bg-white/20 rounded-full transition-colors"
                        >
                            <XMarkIcon className="h-6 w-6" />
                        </button>
                    </div>
                    <p className="text-blue-100 mt-2">Compare up to {properties.length} properties side by side</p>
                </div>

                {/* Content */}
                <div className="overflow-auto max-h-[calc(90vh-120px)]">
                    <div className="p-6">
                        {/* Property Cards */}
                        <div className={`grid gap-6 mb-8 ${
                            properties.length === 2 ? 'grid-cols-2' : 
                            properties.length === 3 ? 'grid-cols-3' : 
                            'grid-cols-4'
                        }`}>
                            {properties.map((property) => (
                                <div key={property.id} className="comparison-card bg-gray-50 rounded-lg overflow-hidden">
                                    {/* Property Image */}
                                    <div className="relative">
                                        <div className="aspect-w-16 aspect-h-9 bg-gray-200">
                                            {property.primary_image ? (
                                                <img
                                                    src={
                                                        property.primary_image.image_path.startsWith('http')
                                                            ? property.primary_image.image_path
                                                            : `/storage/${property.primary_image.image_path}`
                                                    }
                                                    alt={property.primary_image.alt_text || property.title}
                                                    className="w-full h-32 object-cover"
                                                />
                                            ) : (
                                                <div className="w-full h-32 bg-gray-300 flex items-center justify-center">
                                                    <HomeIcon className="w-8 h-8 text-gray-400" />
                                                </div>
                                            )}
                                        </div>
                                        <button
                                            onClick={() => onRemoveProperty(property.id)}
                                            className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                                        >
                                            <XMarkIcon className="h-4 w-4" />
                                        </button>
                                        {property.is_featured && (
                                            <div className="absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-semibold">
                                                Featured
                                            </div>
                                        )}
                                    </div>

                                    {/* Property Info */}
                                    <div className="p-4">
                                        <h3 className="font-semibold text-gray-900 mb-2 text-sm">{property.title}</h3>
                                        <div className="flex items-center text-gray-600 mb-2">
                                            <MapPinIcon className="w-3 h-3 mr-1" />
                                            <span className="text-xs">{property.city}</span>
                                        </div>
                                        <div className="flex items-center mb-2">
                                            <BanknotesIcon className="w-4 h-4 text-green-600 mr-1" />
                                            <span className="text-lg font-bold text-green-600">
                                                {formatPrice(property.price)}
                                            </span>
                                        </div>
                                        {property.average_rating && (
                                            <div className="flex items-center">
                                                <StarIconSolid className="w-4 h-4 text-yellow-400 mr-1" />
                                                <span className="text-sm text-gray-600">
                                                    {property.average_rating.toFixed(1)} ({property.reviews_count} reviews)
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* Comparison Table */}
                        <div className="bg-white rounded-lg border overflow-hidden">
                            <table className="w-full">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Feature</th>
                                        {properties.map((property) => (
                                            <th key={property.id} className="px-4 py-3 text-center text-sm font-medium text-gray-900">
                                                {property.title.substring(0, 20)}...
                                            </th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody className="divide-y divide-gray-200">
                                    {/* Basic Info */}
                                    <tr>
                                        <td className="px-4 py-3 text-sm font-medium text-gray-900">Property Type</td>
                                        {properties.map((property) => (
                                            <td key={property.id} className="px-4 py-3 text-sm text-gray-600 text-center capitalize">
                                                {property.type}
                                            </td>
                                        ))}
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="px-4 py-3 text-sm font-medium text-gray-900">Listing Type</td>
                                        {properties.map((property) => (
                                            <td key={property.id} className="px-4 py-3 text-sm text-gray-600 text-center capitalize">
                                                For {property.listing_type}
                                            </td>
                                        ))}
                                    </tr>
                                    <tr>
                                        <td className="px-4 py-3 text-sm font-medium text-gray-900">Price</td>
                                        {properties.map((property) => (
                                            <td key={property.id} className="px-4 py-3 text-sm font-bold text-green-600 text-center">
                                                {formatPrice(property.price)}
                                                {property.listing_type === 'rent' && <div className="text-xs text-gray-500">/month</div>}
                                            </td>
                                        ))}
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="px-4 py-3 text-sm font-medium text-gray-900">Bedrooms</td>
                                        {properties.map((property) => (
                                            <td key={property.id} className="px-4 py-3 text-sm text-gray-600 text-center">
                                                {property.bedrooms}
                                            </td>
                                        ))}
                                    </tr>
                                    <tr>
                                        <td className="px-4 py-3 text-sm font-medium text-gray-900">Bathrooms</td>
                                        {properties.map((property) => (
                                            <td key={property.id} className="px-4 py-3 text-sm text-gray-600 text-center">
                                                {property.bathrooms}
                                            </td>
                                        ))}
                                    </tr>
                                    <tr className="bg-gray-50">
                                        <td className="px-4 py-3 text-sm font-medium text-gray-900">Floor Area</td>
                                        {properties.map((property) => (
                                            <td key={property.id} className="px-4 py-3 text-sm text-gray-600 text-center">
                                                {property.area ? `${property.area} m²` : 'N/A'}
                                            </td>
                                        ))}
                                    </tr>
                                    <tr>
                                        <td className="px-4 py-3 text-sm font-medium text-gray-900">Listed Date</td>
                                        {properties.map((property) => (
                                            <td key={property.id} className="px-4 py-3 text-sm text-gray-600 text-center">
                                                {formatDate(property.created_at)}
                                            </td>
                                        ))}
                                    </tr>

                                    {/* Amenities */}
                                    {getAllAmenities().length > 0 && (
                                        <>
                                            <tr className="bg-blue-50">
                                                <td colSpan={properties.length + 1} className="px-4 py-2 text-sm font-semibold text-blue-900">
                                                    Amenities
                                                </td>
                                            </tr>
                                            {getAllAmenities().map((amenity, index) => (
                                                <tr key={amenity} className={index % 2 === 0 ? 'bg-gray-50' : ''}>
                                                    <td className="px-4 py-3 text-sm text-gray-900">{amenity}</td>
                                                    {properties.map((property) => (
                                                        <td key={property.id} className="px-4 py-3 text-center">
                                                            {hasAmenity(property, amenity) ? (
                                                                <span className="text-green-600 font-bold">✓</span>
                                                            ) : (
                                                                <span className="text-red-400">✗</span>
                                                            )}
                                                        </td>
                                                    ))}
                                                </tr>
                                            ))}
                                        </>
                                    )}

                                    {/* Features */}
                                    {getAllFeatures().length > 0 && (
                                        <>
                                            <tr className="bg-green-50">
                                                <td colSpan={properties.length + 1} className="px-4 py-2 text-sm font-semibold text-green-900">
                                                    Features
                                                </td>
                                            </tr>
                                            {getAllFeatures().map((feature, index) => (
                                                <tr key={feature} className={index % 2 === 0 ? 'bg-gray-50' : ''}>
                                                    <td className="px-4 py-3 text-sm text-gray-900">{feature}</td>
                                                    {properties.map((property) => (
                                                        <td key={property.id} className="px-4 py-3 text-center">
                                                            {hasFeature(property, feature) ? (
                                                                <span className="text-green-600 font-bold">✓</span>
                                                            ) : (
                                                                <span className="text-red-400">✗</span>
                                                            )}
                                                        </td>
                                                    ))}
                                                </tr>
                                            ))}
                                        </>
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PropertyComparison;
