<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            RolePermissionSeeder::class,
            PropertySeeder::class,
        ]);

        // Create admin user
        $admin = User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'user_type' => 'admin',
            'is_verified' => true,
        ]);
        $admin->assignRole('admin');

        // Create landlord user
        $landlord = User::factory()->create([
            'name' => 'John Landlord',
            'email' => '<EMAIL>',
            'user_type' => 'landlord',
            'is_verified' => true,
        ]);
        $landlord->assignRole('landlord');

        // Create tenant user
        $tenant = User::factory()->create([
            'name' => 'Jane Tenant',
            'email' => '<EMAIL>',
            'user_type' => 'tenant',
            'is_verified' => true,
        ]);
        $tenant->assignRole('tenant');
    }
}
