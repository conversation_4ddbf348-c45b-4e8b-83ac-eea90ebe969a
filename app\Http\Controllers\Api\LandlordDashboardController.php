<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Property;
use App\Models\Booking;
use App\Models\Payment;
use App\Models\Message;
use App\Models\Review;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class LandlordDashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            $user = Auth::user();
            if (!$user->isLandlord() && !$user->isAgent()) {
                return response()->json(['message' => 'Unauthorized'], 403);
            }
            return $next($request);
        });
    }

    /**
     * Get landlord dashboard statistics
     */
    public function stats()
    {
        $userId = Auth::id();

        $stats = [
            'total_properties' => Property::where('user_id', $userId)->count(),
            'active_properties' => Property::where('user_id', $userId)
                ->where('status', 'published')->count(),
            'total_bookings' => Booking::whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->count(),
            'pending_bookings' => Booking::whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->where('status', 'pending')->count(),
            'total_revenue' => Payment::whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->where('status', 'paid')->sum('amount'),
            'monthly_revenue' => Payment::whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->where('status', 'paid')
                ->whereMonth('paid_at', now()->month)
                ->whereYear('paid_at', now()->year)
                ->sum('amount'),
            'unread_messages' => Message::where('receiver_id', $userId)
                ->where('is_read', false)->count(),
            'pending_reviews' => Review::whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->where('is_approved', false)->count(),
        ];

        // Property performance
        $propertyStats = Property::where('user_id', $userId)
            ->withCount(['bookings', 'favorites', 'reviews'])
            ->withAvg('reviews', 'rating')
            ->get(['id', 'title', 'views']);

        // Monthly revenue chart (last 6 months)
        $monthlyRevenue = Payment::select(
            DB::raw('YEAR(paid_at) as year'),
            DB::raw('MONTH(paid_at) as month'),
            DB::raw('SUM(amount) as total')
        )
        ->whereHas('property', function ($q) use ($userId) {
            $q->where('user_id', $userId);
        })
        ->where('status', 'paid')
        ->where('paid_at', '>=', now()->subMonths(6))
        ->groupBy('year', 'month')
        ->orderBy('year', 'asc')
        ->orderBy('month', 'asc')
        ->get();

        return response()->json([
            'stats' => $stats,
            'property_stats' => $propertyStats,
            'monthly_revenue' => $monthlyRevenue,
        ]);
    }

    /**
     * Get landlord's properties
     */
    public function properties(Request $request)
    {
        $query = Property::where('user_id', Auth::id())
            ->with(['primaryImage'])
            ->withCount(['bookings', 'favorites', 'reviews']);

        // Status filter
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Search filter
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('address', 'like', "%{$search}%");
            });
        }

        $properties = $query->orderBy('created_at', 'desc')->paginate(10);

        return response()->json($properties);
    }

    /**
     * Get bookings for landlord's properties
     */
    public function bookings(Request $request)
    {
        $userId = Auth::id();
        $query = Booking::with(['property', 'user'])
            ->whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            });

        // Status filter
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Property filter
        if ($request->has('property_id')) {
            $query->where('property_id', $request->property_id);
        }

        $bookings = $query->orderBy('created_at', 'desc')->paginate(10);

        return response()->json($bookings);
    }

    /**
     * Get payments for landlord's properties
     */
    public function payments(Request $request)
    {
        $userId = Auth::id();
        $query = Payment::with(['property', 'user'])
            ->whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            });

        // Status filter
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Property filter
        if ($request->has('property_id')) {
            $query->where('property_id', $request->property_id);
        }

        $payments = $query->orderBy('created_at', 'desc')->paginate(10);

        return response()->json($payments);
    }

    /**
     * Get messages for landlord
     */
    public function messages()
    {
        $userId = Auth::id();
        
        // Get conversations where user is receiver
        $conversations = Message::select([
            'sender_id as other_user_id',
            'property_id',
            DB::raw('MAX(created_at) as last_message_at'),
            DB::raw('COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_count')
        ])
        ->where('receiver_id', $userId)
        ->groupBy('sender_id', 'property_id')
        ->orderBy('last_message_at', 'desc')
        ->get();

        // Load related data
        $conversations->load([
            'property:id,title,address',
            'property.primaryImage'
        ]);

        return response()->json($conversations);
    }

    /**
     * Get reviews for landlord's properties
     */
    public function reviews(Request $request)
    {
        $userId = Auth::id();
        $query = Review::with(['user', 'property'])
            ->whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            });

        // Status filter
        if ($request->has('status')) {
            if ($request->status === 'pending') {
                $query->where('is_approved', false);
            } elseif ($request->status === 'approved') {
                $query->where('is_approved', true);
            }
        }

        $reviews = $query->orderBy('created_at', 'desc')->paginate(10);

        return response()->json($reviews);
    }

    /**
     * Get recent activities
     */
    public function recentActivities()
    {
        $userId = Auth::id();
        $activities = [];

        // Recent bookings
        $recentBookings = Booking::with(['user', 'property'])
            ->whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })
            ->latest()->take(5)->get();

        foreach ($recentBookings as $booking) {
            $activities[] = [
                'type' => 'booking',
                'description' => "New {$booking->type} booking for {$booking->property->title}",
                'created_at' => $booking->created_at,
                'data' => $booking
            ];
        }

        // Recent payments
        $recentPayments = Payment::with(['user', 'property'])
            ->whereHas('property', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })
            ->latest()->take(5)->get();

        foreach ($recentPayments as $payment) {
            $activities[] = [
                'type' => 'payment',
                'description' => "Payment of ₱{$payment->amount} for {$payment->property->title}",
                'created_at' => $payment->created_at,
                'data' => $payment
            ];
        }

        // Recent messages
        $recentMessages = Message::with(['sender', 'property'])
            ->where('receiver_id', $userId)
            ->latest()->take(5)->get();

        foreach ($recentMessages as $message) {
            $activities[] = [
                'type' => 'message',
                'description' => "New message from {$message->sender->name}",
                'created_at' => $message->created_at,
                'data' => $message
            ];
        }

        // Sort by created_at desc
        usort($activities, function ($a, $b) {
            return $b['created_at'] <=> $a['created_at'];
        });

        return response()->json(array_slice($activities, 0, 15));
    }
}
