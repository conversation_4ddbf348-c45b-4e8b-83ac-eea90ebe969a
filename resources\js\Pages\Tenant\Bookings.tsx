import React from 'react';
import { Head } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { PageProps } from '@/types';
import { CalendarIcon } from '@heroicons/react/24/outline';

interface BookingsPageProps extends PageProps {
    bookings: {
        data: any[];
        meta: any;
    };
}

export default function Bookings({ auth, bookings }: BookingsPageProps) {
    return (
        <DashboardLayout
            user={auth.user}
            header={
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">My Bookings</h1>
                    <p className="text-gray-600">View and manage your property bookings</p>
                </div>
            }
        >
            <Head title="My Bookings - Tenant" />

            <div className="space-y-6">
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <CalendarIcon className="h-8 w-8 text-blue-600 mr-3" />
                        <div>
                            <h2 className="text-lg font-semibold text-gray-900">My Bookings</h2>
                            <p className="text-gray-600">This page is under development</p>
                        </div>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    );
}
