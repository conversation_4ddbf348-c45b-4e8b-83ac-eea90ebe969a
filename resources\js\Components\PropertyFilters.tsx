import React, { useState } from 'react';
import { MagnifyingGlassIcon, FunnelIcon } from '@heroicons/react/24/outline';

interface Filters {
    search?: string;
    type?: string;
    listing_type?: string;
    min_price?: number;
    max_price?: number;
    bedrooms?: number;
    bathrooms?: number;
    city?: string;
    featured?: boolean;
    sort_by?: string;
    sort_order?: string;
}

interface PropertyFiltersProps {
    onFilterChange: (filters: Filters) => void;
}

const PropertyFilters: React.FC<PropertyFiltersProps> = ({ onFilterChange }) => {
    const [filters, setFilters] = useState<Filters>({});
    const [showAdvanced, setShowAdvanced] = useState(false);

    const propertyTypes = [
        { value: '', label: 'All Types' },
        { value: 'apartment', label: 'Apartment' },
        { value: 'house', label: 'House' },
        { value: 'condo', label: 'Condo' },
        { value: 'townhouse', label: 'Townhouse' },
        { value: 'studio', label: 'Studio' },
        { value: 'commercial', label: 'Commercial' },
    ];

    const listingTypes = [
        { value: '', label: 'All Listings' },
        { value: 'rent', label: 'For Rent' },
        { value: 'sale', label: 'For Sale' },
    ];

    const bedroomOptions = [
        { value: '', label: 'Any' },
        { value: '1', label: '1+' },
        { value: '2', label: '2+' },
        { value: '3', label: '3+' },
        { value: '4', label: '4+' },
        { value: '5', label: '5+' },
    ];

    const bathroomOptions = [
        { value: '', label: 'Any' },
        { value: '1', label: '1+' },
        { value: '2', label: '2+' },
        { value: '3', label: '3+' },
        { value: '4', label: '4+' },
    ];

    const sortOptions = [
        { value: 'created_at', label: 'Newest First', order: 'desc' },
        { value: 'created_at', label: 'Oldest First', order: 'asc' },
        { value: 'price', label: 'Price: Low to High', order: 'asc' },
        { value: 'price', label: 'Price: High to Low', order: 'desc' },
        { value: 'title', label: 'Name: A to Z', order: 'asc' },
        { value: 'title', label: 'Name: Z to A', order: 'desc' },
    ];

    const handleFilterChange = (key: string, value: any) => {
        const newFilters = { ...filters, [key]: value };
        setFilters(newFilters);
        onFilterChange(newFilters);
    };

    const handleSortChange = (sortValue: string) => {
        const selectedSort = sortOptions.find(option => 
            `${option.value}_${option.order}` === sortValue
        );
        
        if (selectedSort) {
            const newFilters = {
                ...filters,
                sort_by: selectedSort.value,
                sort_order: selectedSort.order,
            };
            setFilters(newFilters);
            onFilterChange(newFilters);
        }
    };

    const clearFilters = () => {
        setFilters({});
        onFilterChange({});
    };

    return (
        <div className="bg-white p-6 rounded-lg shadow-md mb-6">
            {/* Search Bar */}
            <div className="mb-4">
                <div className="relative">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                        type="text"
                        placeholder="Search properties by title, description, or location..."
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        value={filters.search || ''}
                        onChange={(e) => handleFilterChange('search', e.target.value)}
                    />
                </div>
            </div>

            {/* Basic Filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Property Type
                    </label>
                    <select
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                        value={filters.type || ''}
                        onChange={(e) => handleFilterChange('type', e.target.value)}
                    >
                        {propertyTypes.map((type) => (
                            <option key={type.value} value={type.value}>
                                {type.label}
                            </option>
                        ))}
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Listing Type
                    </label>
                    <select
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                        value={filters.listing_type || ''}
                        onChange={(e) => handleFilterChange('listing_type', e.target.value)}
                    >
                        {listingTypes.map((type) => (
                            <option key={type.value} value={type.value}>
                                {type.label}
                            </option>
                        ))}
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Bedrooms
                    </label>
                    <select
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                        value={filters.bedrooms || ''}
                        onChange={(e) => handleFilterChange('bedrooms', e.target.value)}
                    >
                        {bedroomOptions.map((option) => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Sort By
                    </label>
                    <select
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                        value={filters.sort_by && filters.sort_order ? `${filters.sort_by}_${filters.sort_order}` : 'created_at_desc'}
                        onChange={(e) => handleSortChange(e.target.value)}
                    >
                        {sortOptions.map((option, index) => (
                            <option key={index} value={`${option.value}_${option.order}`}>
                                {option.label}
                            </option>
                        ))}
                    </select>
                </div>
            </div>

            {/* Advanced Filters Toggle */}
            <div className="flex items-center justify-between">
                <button
                    onClick={() => setShowAdvanced(!showAdvanced)}
                    className="flex items-center text-blue-600 hover:text-blue-800 font-medium"
                >
                    <FunnelIcon className="w-4 h-4 mr-1" />
                    {showAdvanced ? 'Hide' : 'Show'} Advanced Filters
                </button>

                <button
                    onClick={clearFilters}
                    className="text-gray-600 hover:text-gray-800 font-medium"
                >
                    Clear All Filters
                </button>
            </div>

            {/* Advanced Filters */}
            {showAdvanced && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Min Price (₱)
                            </label>
                            <input
                                type="number"
                                placeholder="0"
                                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                value={filters.min_price || ''}
                                onChange={(e) => handleFilterChange('min_price', e.target.value ? Number(e.target.value) : undefined)}
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Max Price (₱)
                            </label>
                            <input
                                type="number"
                                placeholder="No limit"
                                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                value={filters.max_price || ''}
                                onChange={(e) => handleFilterChange('max_price', e.target.value ? Number(e.target.value) : undefined)}
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Bathrooms
                            </label>
                            <select
                                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                value={filters.bathrooms || ''}
                                onChange={(e) => handleFilterChange('bathrooms', e.target.value)}
                            >
                                {bathroomOptions.map((option) => (
                                    <option key={option.value} value={option.value}>
                                        {option.label}
                                    </option>
                                ))}
                            </select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                City
                            </label>
                            <input
                                type="text"
                                placeholder="Enter city"
                                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                value={filters.city || ''}
                                onChange={(e) => handleFilterChange('city', e.target.value)}
                            />
                        </div>
                    </div>

                    <div className="mt-4">
                        <label className="flex items-center">
                            <input
                                type="checkbox"
                                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                checked={filters.featured || false}
                                onChange={(e) => handleFilterChange('featured', e.target.checked)}
                            />
                            <span className="ml-2 text-sm text-gray-700">Featured properties only</span>
                        </label>
                    </div>
                </div>
            )}
        </div>
    );
};

export default PropertyFilters;
