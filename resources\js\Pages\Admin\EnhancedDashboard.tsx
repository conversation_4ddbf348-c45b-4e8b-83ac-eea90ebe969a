import React, { useState, useEffect } from 'react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head } from '@inertiajs/react';
import {
    HomeIcon,
    UsersIcon,
    CurrencyDollarIcon,
    ChartBarIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    ClockIcon,
    EyeIcon,
    StarIcon,
    ArrowTrendingUpIcon,
    ArrowTrendingDownIcon
} from '@heroicons/react/24/outline';
import { gsap } from 'gsap';

interface DashboardStats {
    total_properties: number;
    active_properties: number;
    pending_properties: number;
    total_users: number;
    verified_users: number;
    total_bookings: number;
    pending_bookings: number;
    total_revenue: number;
    monthly_revenue: number;
    featured_properties: number;
    total_reviews: number;
    average_rating: number;
}

interface RecentActivity {
    id: number;
    type: 'property_created' | 'user_registered' | 'booking_made' | 'payment_received' | 'review_posted';
    title: string;
    description: string;
    user_name: string;
    created_at: string;
    status?: string;
}

interface EnhancedDashboardProps {
    stats: DashboardStats;
    recentActivities: RecentActivity[];
    monthlyData: Array<{
        month: string;
        properties: number;
        users: number;
        revenue: number;
        bookings: number;
    }>;
    topProperties: Array<{
        id: number;
        title: string;
        views: number;
        bookings: number;
        revenue: number;
        rating: number;
    }>;
}

export default function EnhancedDashboard({
    stats,
    recentActivities,
    monthlyData,
    topProperties
}: EnhancedDashboardProps) {
    const [selectedPeriod, setSelectedPeriod] = useState('month');
    const [activeTab, setActiveTab] = useState('overview');

    useEffect(() => {
        // Animate dashboard elements
        gsap.fromTo('.dashboard-card',
            { y: 30, opacity: 0 },
            { y: 0, opacity: 1, duration: 0.6, stagger: 0.1, ease: "power2.out" }
        );

        gsap.fromTo('.activity-item',
            { x: -20, opacity: 0 },
            { x: 0, opacity: 1, duration: 0.4, stagger: 0.05, delay: 0.3, ease: "power2.out" }
        );
    }, []);

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP',
            minimumFractionDigits: 0,
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-PH', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getActivityIcon = (type: string) => {
        switch (type) {
            case 'property_created': return HomeIcon;
            case 'user_registered': return UsersIcon;
            case 'booking_made': return ClockIcon;
            case 'payment_received': return CurrencyDollarIcon;
            case 'review_posted': return StarIcon;
            default: return CheckCircleIcon;
        }
    };

    const getActivityColor = (type: string) => {
        switch (type) {
            case 'property_created': return 'text-blue-600 bg-blue-100';
            case 'user_registered': return 'text-green-600 bg-green-100';
            case 'booking_made': return 'text-yellow-600 bg-yellow-100';
            case 'payment_received': return 'text-purple-600 bg-purple-100';
            case 'review_posted': return 'text-indigo-600 bg-indigo-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };

    const statCards = [
        {
            title: 'Total Properties',
            value: stats.total_properties,
            change: '+12%',
            trend: 'up',
            icon: HomeIcon,
            color: 'bg-blue-500',
            subtitle: `${stats.active_properties} active, ${stats.pending_properties} pending`
        },
        {
            title: 'Total Users',
            value: stats.total_users,
            change: '+8%',
            trend: 'up',
            icon: UsersIcon,
            color: 'bg-green-500',
            subtitle: `${stats.verified_users} verified users`
        },
        {
            title: 'Monthly Revenue',
            value: formatCurrency(stats.monthly_revenue),
            change: '+15%',
            trend: 'up',
            icon: CurrencyDollarIcon,
            color: 'bg-purple-500',
            subtitle: `${formatCurrency(stats.total_revenue)} total`
        },
        {
            title: 'Total Bookings',
            value: stats.total_bookings,
            change: '+5%',
            trend: 'up',
            icon: ClockIcon,
            color: 'bg-yellow-500',
            subtitle: `${stats.pending_bookings} pending approval`
        },
        {
            title: 'Featured Properties',
            value: stats.featured_properties,
            change: '+3%',
            trend: 'up',
            icon: StarIcon,
            color: 'bg-indigo-500',
            subtitle: 'Premium listings'
        },
        {
            title: 'Average Rating',
            value: stats.average_rating.toFixed(1),
            change: '+0.2',
            trend: 'up',
            icon: StarIcon,
            color: 'bg-pink-500',
            subtitle: `${stats.total_reviews} total reviews`
        },
    ];

    return (
        <AuthenticatedLayout
            header={
                <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold leading-tight text-gray-800">
                        Admin Dashboard
                    </h2>
                    <div className="flex items-center space-x-4">
                        <select
                            value={selectedPeriod}
                            onChange={(e) => setSelectedPeriod(e.target.value)}
                            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                        >
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                            <option value="quarter">This Quarter</option>
                            <option value="year">This Year</option>
                        </select>
                    </div>
                </div>
            }
        >
            <Head title="Admin Dashboard" />

            <div className="py-12">
                <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
                    {/* Stats Grid */}
                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 mb-8">
                        {statCards.map((stat, index) => (
                            <div key={index} className="dashboard-card bg-white overflow-hidden shadow-lg rounded-lg hover:shadow-xl transition-shadow duration-300">
                                <div className="p-6">
                                    <div className="flex items-center">
                                        <div className={`flex-shrink-0 p-3 rounded-lg ${stat.color}`}>
                                            <stat.icon className="h-6 w-6 text-white" />
                                        </div>
                                        <div className="ml-4 flex-1">
                                            <p className="text-sm font-medium text-gray-500 truncate">
                                                {stat.title}
                                            </p>
                                            <div className="flex items-center">
                                                <p className="text-2xl font-semibold text-gray-900">
                                                    {stat.value}
                                                </p>
                                                <div className={`ml-2 flex items-center text-sm ${
                                                    stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                                                }`}>
                                                    {stat.trend === 'up' ? (
                                                        <ArrowTrendingUpIcon className="h-4 w-4 mr-1" />
                                                    ) : (
                                                        <ArrowTrendingDownIcon className="h-4 w-4 mr-1" />
                                                    )}
                                                    {stat.change}
                                                </div>
                                            </div>
                                            <p className="text-xs text-gray-500 mt-1">
                                                {stat.subtitle}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Main Content Grid */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* Recent Activities */}
                        <div className="lg:col-span-2">
                            <div className="dashboard-card bg-white shadow-lg rounded-lg">
                                <div className="p-6">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activities</h3>
                                    <div className="space-y-4 max-h-96 overflow-y-auto">
                                        {recentActivities.map((activity) => {
                                            const ActivityIcon = getActivityIcon(activity.type);
                                            const colorClasses = getActivityColor(activity.type);

                                            return (
                                                <div key={activity.id} className="activity-item flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${colorClasses}`}>
                                                        <ActivityIcon className="h-4 w-4" />
                                                    </div>
                                                    <div className="flex-1 min-w-0">
                                                        <p className="text-sm font-medium text-gray-900">
                                                            {activity.title}
                                                        </p>
                                                        <p className="text-sm text-gray-600">
                                                            {activity.description}
                                                        </p>
                                                        <div className="flex items-center justify-between mt-1">
                                                            <p className="text-xs text-gray-500">
                                                                by {activity.user_name}
                                                            </p>
                                                            <p className="text-xs text-gray-500">
                                                                {formatDate(activity.created_at)}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    {activity.status && (
                                                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                            activity.status === 'approved' ? 'bg-green-100 text-green-800' :
                                                            activity.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                                            'bg-red-100 text-red-800'
                                                        }`}>
                                                            {activity.status}
                                                        </span>
                                                    )}
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Top Properties */}
                        <div className="lg:col-span-1">
                            <div className="dashboard-card bg-white shadow-lg rounded-lg">
                                <div className="p-6">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Performing Properties</h3>
                                    <div className="space-y-4">
                                        {topProperties.map((property, index) => (
                                            <div key={property.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                                <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                                                    {index + 1}
                                                </div>
                                                <div className="flex-1 min-w-0">
                                                    <p className="text-sm font-medium text-gray-900 truncate">
                                                        {property.title}
                                                    </p>
                                                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                                                        <div className="flex items-center">
                                                            <EyeIcon className="h-3 w-3 mr-1" />
                                                            {property.views}
                                                        </div>
                                                        <div className="flex items-center">
                                                            <ClockIcon className="h-3 w-3 mr-1" />
                                                            {property.bookings}
                                                        </div>
                                                        <div className="flex items-center">
                                                            <StarIcon className="h-3 w-3 mr-1" />
                                                            {property.rating.toFixed(1)}
                                                        </div>
                                                    </div>
                                                    <p className="text-xs text-green-600 font-medium">
                                                        {formatCurrency(property.revenue)}
                                                    </p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>

                            {/* Quick Actions */}
                            <div className="dashboard-card bg-white shadow-lg rounded-lg mt-6">
                                <div className="p-6">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                                    <div className="space-y-3">
                                        <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                                            Approve Pending Properties
                                        </button>
                                        <button className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors text-sm">
                                            Verify New Users
                                        </button>
                                        <button className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors text-sm">
                                            Generate Reports
                                        </button>
                                        <button className="w-full bg-yellow-600 text-white py-2 px-4 rounded-lg hover:bg-yellow-700 transition-colors text-sm">
                                            Manage Featured Listings
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Charts Section */}
                    <div className="mt-8">
                        <div className="dashboard-card bg-white shadow-lg rounded-lg">
                            <div className="p-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Overview</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                    {monthlyData.slice(-4).map((data, index) => (
                                        <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                                            <h4 className="text-sm font-medium text-gray-600 mb-2">{data.month}</h4>
                                            <div className="space-y-2">
                                                <div>
                                                    <div className="text-lg font-bold text-blue-600">{data.properties}</div>
                                                    <div className="text-xs text-gray-500">Properties</div>
                                                </div>
                                                <div>
                                                    <div className="text-lg font-bold text-green-600">{data.users}</div>
                                                    <div className="text-xs text-gray-500">New Users</div>
                                                </div>
                                                <div>
                                                    <div className="text-lg font-bold text-purple-600">{formatCurrency(data.revenue)}</div>
                                                    <div className="text-xs text-gray-500">Revenue</div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
