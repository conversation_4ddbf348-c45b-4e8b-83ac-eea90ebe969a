<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Property;
use App\Models\Booking;
use App\Models\Payment;
use App\Models\Review;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class AdminDashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (!Auth::user()->hasRole('admin')) {
                return response()->json(['message' => 'Unauthorized'], 403);
            }
            return $next($request);
        });
    }

    /**
     * Get admin dashboard statistics
     */
    public function stats()
    {
        $stats = [
            'total_users' => User::count(),
            'total_properties' => Property::count(),
            'total_bookings' => Booking::count(),
            'total_payments' => Payment::count(),
            'pending_reviews' => Review::where('is_approved', false)->count(),
            'active_properties' => Property::where('status', 'published')->count(),
            'total_revenue' => Payment::where('status', 'paid')->sum('amount'),
            'monthly_revenue' => Payment::where('status', 'paid')
                ->whereMonth('paid_at', now()->month)
                ->whereYear('paid_at', now()->year)
                ->sum('amount'),
        ];

        // User type breakdown
        $userTypes = User::select('user_type', DB::raw('count(*) as count'))
            ->groupBy('user_type')
            ->pluck('count', 'user_type');

        // Property type breakdown
        $propertyTypes = Property::select('type', DB::raw('count(*) as count'))
            ->groupBy('type')
            ->pluck('count', 'type');

        // Monthly revenue chart data (last 12 months)
        $monthlyRevenue = Payment::select(
            DB::raw('YEAR(paid_at) as year'),
            DB::raw('MONTH(paid_at) as month'),
            DB::raw('SUM(amount) as total')
        )
        ->where('status', 'paid')
        ->where('paid_at', '>=', now()->subMonths(12))
        ->groupBy('year', 'month')
        ->orderBy('year', 'asc')
        ->orderBy('month', 'asc')
        ->get();

        return response()->json([
            'stats' => $stats,
            'user_types' => $userTypes,
            'property_types' => $propertyTypes,
            'monthly_revenue' => $monthlyRevenue,
        ]);
    }

    /**
     * Get all users with pagination and filters
     */
    public function users(Request $request)
    {
        $query = User::query();

        // Search filter
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // User type filter
        if ($request->has('user_type')) {
            $query->where('user_type', $request->user_type);
        }

        // Status filter
        if ($request->has('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $users = $query->withCount(['properties', 'bookings', 'payments'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json($users);
    }

    /**
     * Update user status
     */
    public function updateUserStatus(Request $request, string $userId)
    {
        $validator = Validator::make($request->all(), [
            'is_active' => 'required|boolean',
            'is_verified' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::findOrFail($userId);

        $user->update($request->only(['is_active', 'is_verified']));

        return response()->json([
            'message' => 'User status updated successfully',
            'user' => $user
        ]);
    }

    /**
     * Get all properties with filters
     */
    public function properties(Request $request)
    {
        $query = Property::with(['user', 'primaryImage']);

        // Search filter
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('address', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Type filter
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        $properties = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json($properties);
    }

    /**
     * Update property featured status
     */
    public function updatePropertyFeatured(Request $request, string $propertyId)
    {
        $validator = Validator::make($request->all(), [
            'is_featured' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $property = Property::findOrFail($propertyId);

        $property->update(['is_featured' => $request->is_featured]);

        return response()->json([
            'message' => 'Property featured status updated successfully',
            'property' => $property
        ]);
    }

    /**
     * Get pending reviews
     */
    public function pendingReviews()
    {
        $reviews = Review::with(['user', 'property'])
            ->where('is_approved', false)
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json($reviews);
    }

    /**
     * Approve/reject review
     */
    public function updateReviewStatus(Request $request, string $reviewId)
    {
        $validator = Validator::make($request->all(), [
            'is_approved' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $review = Review::findOrFail($reviewId);

        $review->update(['is_approved' => $request->is_approved]);

        return response()->json([
            'message' => 'Review status updated successfully',
            'review' => $review
        ]);
    }

    /**
     * Get recent activities
     */
    public function recentActivities()
    {
        $activities = [];

        // Recent users
        $recentUsers = User::latest()->take(5)->get(['id', 'name', 'email', 'created_at']);
        foreach ($recentUsers as $user) {
            $activities[] = [
                'type' => 'user_registered',
                'description' => "New user registered: {$user->name}",
                'created_at' => $user->created_at,
                'data' => $user
            ];
        }

        // Recent properties
        $recentProperties = Property::with('user')->latest()->take(5)->get();
        foreach ($recentProperties as $property) {
            $activities[] = [
                'type' => 'property_created',
                'description' => "New property listed: {$property->title}",
                'created_at' => $property->created_at,
                'data' => $property
            ];
        }

        // Recent bookings
        $recentBookings = Booking::with(['user', 'property'])->latest()->take(5)->get();
        foreach ($recentBookings as $booking) {
            $activities[] = [
                'type' => 'booking_created',
                'description' => "New booking for: {$booking->property->title}",
                'created_at' => $booking->created_at,
                'data' => $booking
            ];
        }

        // Sort by created_at desc
        usort($activities, function ($a, $b) {
            return $b['created_at'] <=> $a['created_at'];
        });

        return response()->json(array_slice($activities, 0, 20));
    }
}
