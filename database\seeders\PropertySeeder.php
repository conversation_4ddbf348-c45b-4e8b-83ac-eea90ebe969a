<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Property;
use App\Models\User;

class PropertySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get landlord users
        $landlords = User::where('user_type', 'landlord')->get();

        if ($landlords->isEmpty()) {
            // Create a landlord if none exists
            $landlord = User::create([
                'name' => 'Sample Landlord',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'user_type' => 'landlord',
                'is_verified' => true,
            ]);
            $landlord->assignRole('landlord');
            $landlords = collect([$landlord]);
        }

        $properties = [
            [
                'title' => 'Modern 2BR Condo in Makati CBD',
                'description' => 'Fully furnished 2-bedroom condominium unit in the heart of Makati Central Business District. Features modern amenities, city view, and close to shopping centers and business establishments.',
                'type' => 'condo',
                'listing_type' => 'rent',
                'price' => 45000,
                'bedrooms' => 2,
                'bathrooms' => 2,
                'area' => 65.5,
                'address' => '123 Ayala Avenue',
                'city' => 'Makati',
                'state' => 'Metro Manila',
                'postal_code' => '1226',
                'amenities' => ['Swimming Pool', 'Gym', 'Security', 'Parking', 'WiFi'],
                'features' => ['Air Conditioning', 'Balcony', 'City View', 'Furnished'],
                'is_featured' => true,
                'status' => 'published',
            ],
            [
                'title' => 'Spacious 3BR House in Quezon City',
                'description' => 'Beautiful 3-bedroom house with garden in a quiet subdivision in Quezon City. Perfect for families looking for a peaceful environment while staying connected to the city.',
                'type' => 'house',
                'listing_type' => 'sale',
                'price' => 8500000,
                'bedrooms' => 3,
                'bathrooms' => 2,
                'area' => 120.0,
                'address' => '456 Subdivision Road',
                'city' => 'Quezon City',
                'state' => 'Metro Manila',
                'postal_code' => '1101',
                'amenities' => ['Garden', 'Garage', 'Security'],
                'features' => ['Garage', 'Garden', 'Terrace', 'Storage Room'],
                'is_featured' => true,
                'status' => 'published',
            ],
            [
                'title' => 'Studio Apartment in BGC',
                'description' => 'Cozy studio apartment in Bonifacio Global City. Perfect for young professionals. Walking distance to offices, restaurants, and entertainment centers.',
                'type' => 'studio',
                'listing_type' => 'rent',
                'price' => 25000,
                'bedrooms' => 0,
                'bathrooms' => 1,
                'area' => 30.0,
                'address' => '789 BGC Street',
                'city' => 'Taguig',
                'state' => 'Metro Manila',
                'postal_code' => '1634',
                'amenities' => ['Swimming Pool', 'Gym', 'Security', 'WiFi'],
                'features' => ['Air Conditioning', 'Furnished', 'High Floor'],
                'is_featured' => false,
                'status' => 'published',
            ],
            [
                'title' => 'Luxury 4BR Townhouse in Alabang',
                'description' => 'Elegant 4-bedroom townhouse in an exclusive subdivision in Alabang. Features premium finishes, private garage, and access to clubhouse facilities.',
                'type' => 'townhouse',
                'listing_type' => 'sale',
                'price' => 15000000,
                'bedrooms' => 4,
                'bathrooms' => 3,
                'area' => 200.0,
                'address' => '321 Exclusive Village',
                'city' => 'Muntinlupa',
                'state' => 'Metro Manila',
                'postal_code' => '1780',
                'amenities' => ['Clubhouse', 'Swimming Pool', 'Tennis Court', 'Security', 'Playground'],
                'features' => ['Garage', 'Garden', 'Maid\'s Room', 'Balcony'],
                'is_featured' => true,
                'status' => 'published',
            ],
            [
                'title' => 'Affordable 1BR Apartment in Pasig',
                'description' => 'Budget-friendly 1-bedroom apartment in Pasig City. Great for students or young professionals starting their career. Near public transportation.',
                'type' => 'apartment',
                'listing_type' => 'rent',
                'price' => 15000,
                'bedrooms' => 1,
                'bathrooms' => 1,
                'area' => 25.0,
                'address' => '654 Pasig Road',
                'city' => 'Pasig',
                'state' => 'Metro Manila',
                'postal_code' => '1600',
                'amenities' => ['Security', 'Water Supply'],
                'features' => ['Air Conditioning', 'Kitchen'],
                'is_featured' => false,
                'status' => 'published',
            ],
        ];

        foreach ($properties as $propertyData) {
            Property::create(array_merge($propertyData, [
                'user_id' => $landlords->random()->id,
                'country' => 'Philippines',
                'latitude' => 14.5995 + (rand(-100, 100) / 1000), // Random coordinates around Manila
                'longitude' => 120.9842 + (rand(-100, 100) / 1000),
            ]));
        }
    }
}
