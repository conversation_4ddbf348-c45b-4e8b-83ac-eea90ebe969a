import React from 'react';
import { Head } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { PageProps } from '@/types';
import { HeartIcon } from '@heroicons/react/24/outline';

interface FavoritesPageProps extends PageProps {
    favorites: {
        data: any[];
        meta: any;
    };
}

export default function Favorites({ auth, favorites }: FavoritesPageProps) {
    return (
        <DashboardLayout
            user={auth.user}
            header={
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Favorite Properties</h1>
                    <p className="text-gray-600">Properties you've saved for later</p>
                </div>
            }
        >
            <Head title="Favorite Properties - Tenant" />

            <div className="space-y-6">
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <HeartIcon className="h-8 w-8 text-pink-600 mr-3" />
                        <div>
                            <h2 className="text-lg font-semibold text-gray-900">Favorite Properties</h2>
                            <p className="text-gray-600">This page is under development</p>
                        </div>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    );
}
