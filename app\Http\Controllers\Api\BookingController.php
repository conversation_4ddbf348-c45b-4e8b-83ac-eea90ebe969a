<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Property;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class BookingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $query = Booking::with(['property.primaryImage', 'user']);

        // Filter based on user role
        if ($user->isLandlord() || $user->isAgent()) {
            // Show bookings for properties owned by this user
            $query->whereHas('property', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            });
        } else {
            // Show bookings made by this user
            $query->where('user_id', $user->id);
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        $bookings = $query->orderBy('created_at', 'desc')->paginate(10);

        return response()->json($bookings);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'property_id' => 'required|exists:properties,id',
            'type' => 'required|in:viewing,rental_application,purchase_inquiry',
            'preferred_date' => 'required|date|after:now',
            'alternative_date' => 'nullable|date|after:now',
            'message' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $property = Property::findOrFail($request->property_id);

        // Check if property is available
        if (!$property->is_available || $property->status !== 'published') {
            return response()->json([
                'message' => 'Property is not available for booking'
            ], 400);
        }

        // Check if user is not the property owner
        if ($property->user_id === Auth::id()) {
            return response()->json([
                'message' => 'You cannot book your own property'
            ], 400);
        }

        $booking = Booking::create([
            'property_id' => $request->property_id,
            'user_id' => Auth::id(),
            'type' => $request->type,
            'preferred_date' => $request->preferred_date,
            'alternative_date' => $request->alternative_date,
            'message' => $request->message,
            'status' => 'pending',
        ]);

        $booking->load(['property.primaryImage', 'user']);

        return response()->json([
            'message' => 'Booking created successfully',
            'booking' => $booking
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $booking = Booking::with(['property.primaryImage', 'user'])->findOrFail($id);

        // Check authorization
        $user = Auth::user();
        if ($booking->user_id !== $user->id && $booking->property->user_id !== $user->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return response()->json($booking);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $booking = Booking::findOrFail($id);

        // Check authorization - only the booking creator can update
        if ($booking->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Only allow updates if booking is still pending
        if ($booking->status !== 'pending') {
            return response()->json([
                'message' => 'Cannot update booking that is not pending'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'preferred_date' => 'sometimes|date|after:now',
            'alternative_date' => 'nullable|date|after:now',
            'message' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $booking->update($request->only(['preferred_date', 'alternative_date', 'message']));
        $booking->load(['property.primaryImage', 'user']);

        return response()->json([
            'message' => 'Booking updated successfully',
            'booking' => $booking
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $booking = Booking::findOrFail($id);

        // Check authorization
        if ($booking->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Only allow deletion if booking is pending
        if ($booking->status !== 'pending') {
            return response()->json([
                'message' => 'Cannot delete booking that is not pending'
            ], 400);
        }

        $booking->delete();

        return response()->json(['message' => 'Booking deleted successfully']);
    }

    /**
     * Get user's bookings
     */
    public function myBookings()
    {
        $bookings = Booking::with(['property.primaryImage'])
            ->where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json($bookings);
    }

    /**
     * Update booking status (for property owners)
     */
    public function updateStatus(Request $request, string $id)
    {
        $booking = Booking::findOrFail($id);

        // Check authorization - only property owner can update status
        if ($booking->property->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:confirmed,cancelled,completed',
            'confirmed_date' => 'required_if:status,confirmed|date|after:now',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $booking->update([
            'status' => $request->status,
            'confirmed_date' => $request->confirmed_date,
            'notes' => $request->notes,
        ]);

        $booking->load(['property.primaryImage', 'user']);

        return response()->json([
            'message' => 'Booking status updated successfully',
            'booking' => $booking
        ]);
    }
}
