import React, { useState, useEffect, useRef } from 'react';
import { PageProps } from '@/types';
import { Head, Link } from '@inertiajs/react';
import PropertyCard from '@/Components/PropertyCard';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import {
    MagnifyingGlassIcon,
    HomeIcon,
    MapPinIcon,
    StarIcon,
    HeartIcon,
    BanknotesIcon,
    BuildingOfficeIcon,
    UserGroupIcon,
    ShieldCheckIcon,
    PhoneIcon,
    EnvelopeIcon,
    CheckCircleIcon,
    ClockIcon,
    CurrencyDollarIcon,
    UsersIcon,
    BuildingOffice2Icon,
    KeyIcon,
    DocumentCheckIcon,
    ChatBubbleLeftRightIcon,
    ArrowRightIcon,
    PlayCircleIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

interface Property {
    id: number;
    title: string;
    description: string;
    type: string;
    listing_type: string;
    price: number;
    bedrooms: number;
    bathrooms: number;
    area?: number;
    address: string;
    city: string;
    state: string;
    primary_image?: {
        image_path: string;
        alt_text?: string;
    };
    user: {
        name: string;
    };
    is_featured: boolean;
    created_at: string;
}

export default function Welcome({
    auth,
    laravelVersion,
    phpVersion,
    featuredProperties = [],
}: PageProps<{
    laravelVersion: string;
    phpVersion: string;
    featuredProperties: Property[];
}>) {
    const [searchQuery, setSearchQuery] = useState('');
    const [searchLocation, setSearchLocation] = useState('');

    // Refs for animation targets
    const heroRef = useRef<HTMLElement>(null);
    const featuresRef = useRef<HTMLElement>(null);
    const propertyTypesRef = useRef<HTMLElement>(null);
    const statsRef = useRef<HTMLElement>(null);
    const howItWorksRef = useRef<HTMLElement>(null);
    const testimonialsRef = useRef<HTMLElement>(null);
    const ctaRef = useRef<HTMLElement>(null);
    const progressBarRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        // Register ScrollTrigger plugin
        gsap.registerPlugin(ScrollTrigger);

        // Hero section animations
        const heroTl = gsap.timeline();
        heroTl
            .from(".hero-title", {
                duration: 1.2,
                y: 100,
                opacity: 0,
                ease: "power3.out"
            })
            .from(".hero-subtitle", {
                duration: 1,
                y: 50,
                opacity: 0,
                ease: "power3.out"
            }, "-=0.8")
            .from(".hero-search", {
                duration: 1,
                y: 50,
                opacity: 0,
                scale: 0.9,
                ease: "back.out(1.7)"
            }, "-=0.6");

        // Animate sections on scroll
        const sections = [
            { ref: featuresRef, selector: ".feature-card", stagger: 0.2 },
            { ref: propertyTypesRef, selector: ".property-type-card", stagger: 0.1 },
            { ref: statsRef, selector: ".stat-item", stagger: 0.15 },
            { ref: howItWorksRef, selector: ".step-card", stagger: 0.3 },
            { ref: testimonialsRef, selector: ".testimonial-card", stagger: 0.2 }
        ];

        sections.forEach(({ ref, selector, stagger }) => {
            if (ref.current) {
                gsap.fromTo(ref.current.querySelectorAll(selector),
                    {
                        y: 80,
                        opacity: 0,
                        scale: 0.9
                    },
                    {
                        y: 0,
                        opacity: 1,
                        scale: 1,
                        duration: 0.8,
                        stagger: stagger,
                        ease: "power3.out",
                        scrollTrigger: {
                            trigger: ref.current,
                            start: "top 80%",
                            end: "bottom 20%",
                            toggleActions: "play none none reverse"
                        }
                    }
                );
            }
        });

        // Featured properties animation
        gsap.fromTo(".property-card",
            {
                y: 60,
                opacity: 0,
                rotationY: 15
            },
            {
                y: 0,
                opacity: 1,
                rotationY: 0,
                duration: 0.8,
                stagger: 0.15,
                ease: "power3.out",
                scrollTrigger: {
                    trigger: ".featured-properties",
                    start: "top 75%",
                    toggleActions: "play none none reverse"
                }
            }
        );

        // CTA section animation
        if (ctaRef.current) {
            gsap.fromTo(ctaRef.current,
                {
                    scale: 0.9,
                    opacity: 0
                },
                {
                    scale: 1,
                    opacity: 1,
                    duration: 1,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: ctaRef.current,
                        start: "top 80%",
                        toggleActions: "play none none reverse"
                    }
                }
            );
        }

        // Scroll progress bar animation
        if (progressBarRef.current) {
            gsap.to(progressBarRef.current, {
                scaleX: 1,
                transformOrigin: "left center",
                ease: "none",
                scrollTrigger: {
                    trigger: "body",
                    start: "top top",
                    end: "bottom bottom",
                    scrub: 0.3
                }
            });
        }

        // Parallax effect for hero background
        gsap.to(".hero-bg", {
            yPercent: -50,
            ease: "none",
            scrollTrigger: {
                trigger: heroRef.current,
                start: "top bottom",
                end: "bottom top",
                scrub: true
            }
        });

        // Cleanup function
        return () => {
            ScrollTrigger.getAll().forEach(trigger => trigger.kill());
        };
    }, []);

    const handleSearch = () => {
        const params = new URLSearchParams();
        if (searchQuery) params.append('search', searchQuery);
        if (searchLocation) params.append('city', searchLocation);

        window.location.href = `/properties?${params.toString()}`;
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP',
            minimumFractionDigits: 0,
        }).format(price);
    };

    return (
        <>
            <Head title="Property Listing Platform - Find Your Perfect Home" />

            {/* Scroll Progress Bar */}
            <div className="fixed top-0 left-0 w-full h-1 bg-gray-200 z-50">
                <div
                    ref={progressBarRef}
                    className="h-full bg-gradient-to-r from-blue-600 to-indigo-600 transform scale-x-0"
                ></div>
            </div>

            <div className="min-h-screen bg-white">
                {/* Navigation Header */}
                <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40 backdrop-blur-sm bg-white/95">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="flex justify-between items-center py-4">
                            <div className="flex items-center group">
                                <HomeIcon className="h-8 w-8 text-blue-600 mr-2 group-hover:scale-110 transition-transform duration-300" />
                                <h1 className="text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">PropertyHub</h1>
                            </div>
                            <nav className="flex items-center space-x-4">
                                <Link
                                    href="/properties"
                                    className="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 hover:bg-blue-50"
                                >
                                    Browse Properties
                                </Link>
                                {auth.user ? (
                                    <Link
                                        href="/dashboard"
                                        className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 hover:scale-105 active:scale-95 transition-all duration-200 shadow-md hover:shadow-lg"
                                    >
                                        Dashboard
                                    </Link>
                                ) : (
                                    <div className="flex items-center space-x-2">
                                        <Link
                                            href="/login"
                                            className="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 hover:bg-blue-50"
                                        >
                                            Sign In
                                        </Link>
                                        <Link
                                            href="/register"
                                            className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 hover:scale-105 active:scale-95 transition-all duration-200 shadow-md hover:shadow-lg"
                                        >
                                            Get Started
                                        </Link>
                                    </div>
                                )}
                            </nav>
                        </div>
                    </div>
                </header>

                {/* Hero Section */}
                <section ref={heroRef} className="relative bg-gradient-to-br from-blue-50 to-indigo-100 py-20 overflow-hidden">
                    {/* Parallax Background Elements */}
                    <div className="hero-bg absolute inset-0 opacity-10">
                        <div className="absolute top-10 left-10 w-32 h-32 bg-blue-300 rounded-full blur-xl"></div>
                        <div className="absolute top-40 right-20 w-24 h-24 bg-indigo-300 rounded-full blur-lg"></div>
                        <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-purple-300 rounded-full blur-2xl"></div>
                        <div className="absolute bottom-10 right-10 w-28 h-28 bg-blue-400 rounded-full blur-lg"></div>
                    </div>
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
                        <div className="text-center">
                            <h1 className="hero-title text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                                Find Your Perfect
                                <span className="text-blue-600"> Home</span>
                            </h1>
                            <p className="hero-subtitle text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                                Discover amazing properties for rent and sale across the Philippines.
                                Your dream home is just a search away.
                            </p>

                            {/* Search Bar */}
                            <div className="hero-search max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div className="relative group">
                                        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors" />
                                        <input
                                            type="text"
                                            placeholder="Search properties..."
                                            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                            value={searchQuery}
                                            onChange={(e) => setSearchQuery(e.target.value)}
                                        />
                                    </div>
                                    <div className="relative group">
                                        <MapPinIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors" />
                                        <input
                                            type="text"
                                            placeholder="Location (e.g., Makati, BGC)"
                                            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                            value={searchLocation}
                                            onChange={(e) => setSearchLocation(e.target.value)}
                                        />
                                    </div>
                                    <button
                                        onClick={handleSearch}
                                        className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 hover:scale-105 active:scale-95 transition-all duration-200 font-medium shadow-md hover:shadow-lg"
                                    >
                                        Search Properties
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Featured Properties Section */}
                {featuredProperties.length > 0 && (
                    <section className="featured-properties py-16 bg-gray-50">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="text-center mb-12">
                                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                                    Featured Properties
                                </h2>
                                <p className="text-lg text-gray-600">
                                    Discover our handpicked selection of premium properties
                                </p>
                            </div>
                            <div className="property-grid-3 mb-8">
                                {featuredProperties.slice(0, 6).map((property) => (
                                    <div key={property.id} className="property-card">
                                        <PropertyCard
                                            property={property}
                                            showFavoriteButton={false}
                                        />
                                    </div>
                                ))}
                            </div>
                            <div className="text-center">
                                <Link
                                    href="/properties"
                                    className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 hover:scale-105 active:scale-95 transition-all duration-200 shadow-md hover:shadow-lg"
                                >
                                    View All Properties
                                    <ArrowRightIcon className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                                </Link>
                            </div>
                        </div>
                    </section>
                )}

                {/* Features Section */}
                <section ref={featuresRef} className="py-16 bg-white">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold text-gray-900 mb-4">
                                Why Choose PropertyHub?
                            </h2>
                            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                                We make finding and listing properties simple, secure, and efficient
                            </p>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            <div className="feature-card text-center p-6 rounded-lg hover:bg-gray-50 transition-all duration-300 hover:shadow-lg group">
                                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                                    <ShieldCheckIcon className="h-8 w-8 text-blue-600" />
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                    Verified Listings
                                </h3>
                                <p className="text-gray-600">
                                    All properties are verified by our team to ensure authenticity and quality
                                </p>
                            </div>
                            <div className="feature-card text-center p-6 rounded-lg hover:bg-gray-50 transition-all duration-300 hover:shadow-lg group">
                                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                                    <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                    Best Prices
                                </h3>
                                <p className="text-gray-600">
                                    Competitive pricing with no hidden fees. Find the best deals in the market
                                </p>
                            </div>
                            <div className="feature-card text-center p-6 rounded-lg hover:bg-gray-50 transition-all duration-300 hover:shadow-lg group">
                                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                                    <UsersIcon className="h-8 w-8 text-purple-600" />
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                    Expert Support
                                </h3>
                                <p className="text-gray-600">
                                    Our experienced team is here to help you every step of the way
                                </p>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Property Types Section */}
                <section ref={propertyTypesRef} className="py-16 bg-gray-50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold text-gray-900 mb-4">
                                Property Types
                            </h2>
                            <p className="text-lg text-gray-600">
                                Find the perfect property type for your needs
                            </p>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
                            {[
                                { type: 'apartment', icon: BuildingOffice2Icon, label: 'Apartments' },
                                { type: 'house', icon: HomeIcon, label: 'Houses' },
                                { type: 'condo', icon: BuildingOfficeIcon, label: 'Condos' },
                                { type: 'townhouse', icon: HomeIcon, label: 'Townhouses' },
                                { type: 'studio', icon: KeyIcon, label: 'Studios' },
                                { type: 'commercial', icon: BuildingOffice2Icon, label: 'Commercial' },
                            ].map((item) => (
                                <Link
                                    key={item.type}
                                    href={`/properties?type=${item.type}`}
                                    className="property-type-card group bg-white rounded-lg p-6 text-center hover:shadow-lg transition-all duration-300 hover:-translate-y-2 hover:scale-105 active:scale-95"
                                >
                                    <item.icon className="h-12 w-12 text-blue-600 mx-auto mb-3 group-hover:text-blue-700 group-hover:scale-110 transition-all duration-300" />
                                    <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                                        {item.label}
                                    </h3>
                                </Link>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Statistics Section */}
                <section ref={statsRef} className="py-16 bg-gradient-to-r from-blue-600 to-indigo-700 relative overflow-hidden">
                    <div className="absolute inset-0 bg-black opacity-10"></div>
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                            <div className="stat-item">
                                <div className="text-4xl font-bold text-white mb-2 hover:scale-110 transition-transform duration-300">10K+</div>
                                <div className="text-blue-100">Properties Listed</div>
                            </div>
                            <div className="stat-item">
                                <div className="text-4xl font-bold text-white mb-2 hover:scale-110 transition-transform duration-300">5K+</div>
                                <div className="text-blue-100">Happy Clients</div>
                            </div>
                            <div className="stat-item">
                                <div className="text-4xl font-bold text-white mb-2 hover:scale-110 transition-transform duration-300">50+</div>
                                <div className="text-blue-100">Cities Covered</div>
                            </div>
                            <div className="stat-item">
                                <div className="text-4xl font-bold text-white mb-2 hover:scale-110 transition-transform duration-300">24/7</div>
                                <div className="text-blue-100">Customer Support</div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* How It Works Section */}
                <section ref={howItWorksRef} className="py-16 bg-white">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold text-gray-900 mb-4">
                                How It Works
                            </h2>
                            <p className="text-lg text-gray-600">
                                Simple steps to find your perfect property
                            </p>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <div className="step-card text-center group">
                                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-600 group-hover:scale-110 transition-all duration-300">
                                    <span className="text-2xl font-bold text-blue-600 group-hover:text-white transition-colors duration-300">1</span>
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                                    Search Properties
                                </h3>
                                <p className="text-gray-600">
                                    Use our advanced search filters to find properties that match your criteria
                                </p>
                            </div>
                            <div className="step-card text-center group">
                                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-600 group-hover:scale-110 transition-all duration-300">
                                    <span className="text-2xl font-bold text-blue-600 group-hover:text-white transition-colors duration-300">2</span>
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                                    Schedule Viewing
                                </h3>
                                <p className="text-gray-600">
                                    Contact property owners directly or schedule a viewing through our platform
                                </p>
                            </div>
                            <div className="step-card text-center group">
                                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-600 group-hover:scale-110 transition-all duration-300">
                                    <span className="text-2xl font-bold text-blue-600 group-hover:text-white transition-colors duration-300">3</span>
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                                    Move In
                                </h3>
                                <p className="text-gray-600">
                                    Complete the paperwork and move into your new home with our support
                                </p>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Testimonials Section */}
                <section ref={testimonialsRef} className="py-16 bg-gray-50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold text-gray-900 mb-4">
                                What Our Clients Say
                            </h2>
                            <p className="text-lg text-gray-600">
                                Real stories from satisfied customers
                            </p>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            {[
                                {
                                    name: "Maria Santos",
                                    location: "Makati City",
                                    text: "PropertyHub made finding my dream condo so easy! The search filters were perfect and the support team was incredibly helpful.",
                                    rating: 5
                                },
                                {
                                    name: "John Dela Cruz",
                                    location: "Quezon City",
                                    text: "I listed my property and found a tenant within a week. The platform is user-friendly and the verification process gives me confidence.",
                                    rating: 5
                                },
                                {
                                    name: "Sarah Kim",
                                    location: "BGC, Taguig",
                                    text: "Excellent service! The property details were accurate and the virtual tours saved me so much time. Highly recommended!",
                                    rating: 5
                                }
                            ].map((testimonial, index) => (
                                <div key={index} className="testimonial-card bg-white rounded-lg p-6 shadow-md hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group">
                                    <div className="flex items-center mb-4">
                                        {[...Array(testimonial.rating)].map((_, i) => (
                                            <StarIconSolid key={i} className="h-5 w-5 text-yellow-400 group-hover:scale-110 transition-transform duration-300" style={{transitionDelay: `${i * 50}ms`}} />
                                        ))}
                                    </div>
                                    <p className="text-gray-600 mb-4 italic group-hover:text-gray-700 transition-colors duration-300">"{testimonial.text}"</p>
                                    <div>
                                        <div className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">{testimonial.name}</div>
                                        <div className="text-sm text-gray-500">{testimonial.location}</div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Call to Action Section */}
                <section ref={ctaRef} className="py-16 bg-gradient-to-r from-blue-600 to-indigo-700 relative overflow-hidden">
                    <div className="absolute inset-0 bg-black opacity-10"></div>
                    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
                        <h2 className="text-3xl font-bold text-white mb-4">
                            Ready to Find Your Perfect Property?
                        </h2>
                        <p className="text-xl text-blue-100 mb-8">
                            Join thousands of satisfied customers who found their dream homes with PropertyHub
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Link
                                href="/register"
                                className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 hover:scale-105 active:scale-95 transition-all duration-200 shadow-lg hover:shadow-xl"
                            >
                                Get Started Free
                            </Link>
                            <Link
                                href="/properties"
                                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 hover:scale-105 active:scale-95 transition-all duration-200 shadow-lg hover:shadow-xl"
                            >
                                Browse Properties
                            </Link>
                        </div>
                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-gray-900 text-white py-12">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                            <div>
                                <div className="flex items-center mb-4">
                                    <HomeIcon className="h-8 w-8 text-blue-400 mr-2" />
                                    <h3 className="text-xl font-bold">PropertyHub</h3>
                                </div>
                                <p className="text-gray-400 mb-4">
                                    Your trusted partner in finding the perfect property in the Philippines.
                                </p>
                                <div className="flex space-x-4">
                                    <a href="#" className="text-gray-400 hover:text-white">
                                        <span className="sr-only">Facebook</span>
                                        <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/>
                                        </svg>
                                    </a>
                                    <a href="#" className="text-gray-400 hover:text-white">
                                        <span className="sr-only">Twitter</span>
                                        <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                            <div>
                                <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
                                <ul className="space-y-2">
                                    <li><Link href="/properties" className="text-gray-400 hover:text-white">Browse Properties</Link></li>
                                    <li><Link href="/properties?listing_type=rent" className="text-gray-400 hover:text-white">For Rent</Link></li>
                                    <li><Link href="/properties?listing_type=sale" className="text-gray-400 hover:text-white">For Sale</Link></li>
                                    <li><Link href="/properties?featured=1" className="text-gray-400 hover:text-white">Featured</Link></li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="text-lg font-semibold mb-4">Support</h4>
                                <ul className="space-y-2">
                                    <li><a href="#" className="text-gray-400 hover:text-white">Help Center</a></li>
                                    <li><a href="#" className="text-gray-400 hover:text-white">Contact Us</a></li>
                                    <li><a href="#" className="text-gray-400 hover:text-white">Terms of Service</a></li>
                                    <li><a href="#" className="text-gray-400 hover:text-white">Privacy Policy</a></li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="text-lg font-semibold mb-4">Contact Info</h4>
                                <div className="space-y-2">
                                    <div className="flex items-center">
                                        <PhoneIcon className="h-5 w-5 text-gray-400 mr-2" />
                                        <span className="text-gray-400">+63 2 123 4567</span>
                                    </div>
                                    <div className="flex items-center">
                                        <EnvelopeIcon className="h-5 w-5 text-gray-400 mr-2" />
                                        <span className="text-gray-400"><EMAIL></span>
                                    </div>
                                    <div className="flex items-center">
                                        <MapPinIcon className="h-5 w-5 text-gray-400 mr-2" />
                                        <span className="text-gray-400">Manila, Philippines</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="border-t border-gray-800 mt-8 pt-8 text-center">
                            <p className="text-gray-400">
                                © 2025 PropertyHub. All rights reserved. | Laravel v{laravelVersion} (PHP v{phpVersion})
                            </p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
