<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Booking extends Model
{
    use HasFactory;

    protected $fillable = [
        'property_id',
        'user_id',
        'type',
        'preferred_date',
        'alternative_date',
        'message',
        'status',
        'confirmed_date',
        'notes',
    ];

    protected function casts(): array
    {
        return [
            'preferred_date' => 'datetime',
            'alternative_date' => 'datetime',
            'confirmed_date' => 'datetime',
        ];
    }

    // Relationships
    public function property()
    {
        return $this->belongsTo(Property::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }
}
