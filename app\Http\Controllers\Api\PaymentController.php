<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Property;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Xendit\Xendit;
use Xendit\Invoice;

class PaymentController extends Controller
{
    public function __construct()
    {
        Xendit::setApiKey(config('services.xendit.secret_key'));
    }

    /**
     * Display a listing of payments
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $query = Payment::with(['property', 'user']);

        // Filter based on user role
        if ($user->isLandlord() || $user->isAgent()) {
            // Show payments for properties owned by this user
            $query->whereHas('property', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            });
        } else {
            // Show payments made by this user
            $query->where('user_id', $user->id);
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        $payments = $query->orderBy('created_at', 'desc')->paginate(10);

        return response()->json($payments);
    }

    /**
     * Create a new payment
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'property_id' => 'required|exists:properties,id',
            'payment_type' => 'required|in:rent,deposit,booking_fee,purchase',
            'amount' => 'required|numeric|min:1',
            'payment_method' => 'required|in:xendit,cod',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $property = Property::findOrFail($request->property_id);
        $user = Auth::user();

        // Create payment record
        $payment = Payment::create([
            'user_id' => $user->id,
            'property_id' => $property->id,
            'payment_type' => $request->payment_type,
            'amount' => $request->amount,
            'currency' => 'PHP',
            'status' => $request->payment_method === 'cod' ? 'pending' : 'pending',
            'payment_method' => $request->payment_method,
            'notes' => $request->notes,
        ]);

        if ($request->payment_method === 'xendit') {
            try {
                // Create Xendit invoice
                $invoice = Invoice::create([
                    'external_id' => 'payment_' . $payment->id . '_' . time(),
                    'payer_email' => $user->email,
                    'description' => ucfirst($request->payment_type) . ' for ' . $property->title,
                    'amount' => $request->amount,
                    'currency' => 'PHP',
                    'success_redirect_url' => config('app.url') . '/payments/success',
                    'failure_redirect_url' => config('app.url') . '/payments/failed',
                ]);

                $payment->update([
                    'xendit_payment_id' => $invoice['id'],
                    'payment_details' => [
                        'invoice_url' => $invoice['invoice_url'],
                        'external_id' => $invoice['external_id'],
                    ]
                ]);

                return response()->json([
                    'message' => 'Payment created successfully',
                    'payment' => $payment,
                    'payment_url' => $invoice['invoice_url']
                ], 201);

            } catch (\Exception $e) {
                Log::error('Xendit payment creation failed: ' . $e->getMessage());

                $payment->update(['status' => 'failed']);

                return response()->json([
                    'message' => 'Payment creation failed',
                    'error' => 'Unable to create payment gateway'
                ], 500);
            }
        }

        return response()->json([
            'message' => 'Payment created successfully',
            'payment' => $payment
        ], 201);
    }

    /**
     * Display the specified payment
     */
    public function show(string $id)
    {
        $payment = Payment::with(['property', 'user'])->findOrFail($id);

        // Check authorization
        $user = Auth::user();
        if ($payment->user_id !== $user->id && $payment->property->user_id !== $user->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return response()->json($payment);
    }

    /**
     * Update payment status
     */
    public function update(Request $request, string $id)
    {
        $payment = Payment::findOrFail($id);

        // Check authorization - only property owner can update
        if ($payment->property->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:paid,failed,cancelled',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $payment->update([
            'status' => $request->status,
            'paid_at' => $request->status === 'paid' ? now() : null,
            'notes' => $request->notes,
        ]);

        return response()->json([
            'message' => 'Payment status updated successfully',
            'payment' => $payment
        ]);
    }

    /**
     * Handle Xendit webhook callback
     */
    public function xenditCallback(Request $request)
    {
        $callbackToken = $request->header('x-callback-token');

        // Verify callback token
        if ($callbackToken !== config('services.xendit.callback_token')) {
            return response()->json(['message' => 'Invalid callback token'], 401);
        }

        $data = $request->all();

        try {
            // Find payment by external_id
            $externalId = $data['external_id'] ?? null;
            if (!$externalId) {
                return response()->json(['message' => 'External ID not found'], 400);
            }

            $payment = Payment::where('payment_details->external_id', $externalId)->first();

            if (!$payment) {
                Log::warning('Payment not found for external_id: ' . $externalId);
                return response()->json(['message' => 'Payment not found'], 404);
            }

            // Update payment status based on Xendit status
            $status = strtolower($data['status'] ?? '');

            switch ($status) {
                case 'paid':
                    $payment->update([
                        'status' => 'paid',
                        'paid_at' => now(),
                        'transaction_id' => $data['id'] ?? null,
                        'payment_details' => array_merge($payment->payment_details ?? [], $data)
                    ]);
                    break;

                case 'expired':
                case 'failed':
                    $payment->update([
                        'status' => 'failed',
                        'payment_details' => array_merge($payment->payment_details ?? [], $data)
                    ]);
                    break;
            }

            Log::info('Xendit callback processed successfully', ['payment_id' => $payment->id, 'status' => $status]);

            return response()->json(['message' => 'Callback processed successfully']);

        } catch (\Exception $e) {
            Log::error('Xendit callback processing failed: ' . $e->getMessage(), ['data' => $data]);
            return response()->json(['message' => 'Callback processing failed'], 500);
        }
    }

    /**
     * Get user's payments
     */
    public function myPayments()
    {
        $payments = Payment::with(['property'])
            ->where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json($payments);
    }

    /**
     * Generate payment receipt/invoice
     */
    public function generateReceipt(string $id)
    {
        $payment = Payment::with(['property', 'user'])->findOrFail($id);

        // Check authorization
        $user = Auth::user();
        if ($payment->user_id !== $user->id && $payment->property->user_id !== $user->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Only generate receipt for paid payments
        if ($payment->status !== 'paid') {
            return response()->json(['message' => 'Payment not completed'], 400);
        }

        try {
            $pdf = app('dompdf.wrapper');
            $pdf->loadView('receipts.payment', compact('payment'));

            $filename = 'receipt_' . $payment->id . '.pdf';

            return $pdf->download($filename);

        } catch (\Exception $e) {
            Log::error('Receipt generation failed: ' . $e->getMessage());
            return response()->json(['message' => 'Receipt generation failed'], 500);
        }
    }

    /**
     * Delete a payment
     */
    public function destroy(string $id)
    {
        $payment = Payment::findOrFail($id);

        // Check authorization - only payment creator can delete
        if ($payment->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Only allow deletion if payment is pending or failed
        if (!in_array($payment->status, ['pending', 'failed'])) {
            return response()->json([
                'message' => 'Cannot delete completed payment'
            ], 400);
        }

        $payment->delete();

        return response()->json(['message' => 'Payment deleted successfully']);
    }
}
