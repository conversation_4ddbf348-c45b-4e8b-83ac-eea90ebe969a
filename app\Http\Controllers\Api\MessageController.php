<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Message;
use App\Models\Property;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class MessageController extends Controller
{
    /**
     * Display a listing of conversations
     */
    public function index()
    {
        $userId = Auth::id();

        // Get all conversations for the authenticated user
        $conversations = Message::select([
            DB::raw('CASE
                WHEN sender_id = ' . $userId . ' THEN receiver_id
                ELSE sender_id
            END as other_user_id'),
            'property_id',
            DB::raw('MAX(created_at) as last_message_at'),
            DB::raw('COUNT(CASE WHEN receiver_id = ' . $userId . ' AND is_read = 0 THEN 1 END) as unread_count')
        ])
        ->where(function ($query) use ($userId) {
            $query->where('sender_id', $userId)
                  ->orWhere('receiver_id', $userId);
        })
        ->groupBy('other_user_id', 'property_id')
        ->orderBy('last_message_at', 'desc')
        ->get();

        // Load related data
        $conversations->load([
            'property:id,title,address',
            'property.primaryImage'
        ]);

        // Get other user details
        $otherUserIds = $conversations->pluck('other_user_id')->unique();
        $otherUsers = User::whereIn('id', $otherUserIds)->get()->keyBy('id');

        $conversations->each(function ($conversation) use ($otherUsers) {
            $conversation->other_user = $otherUsers->get($conversation->other_user_id);
        });

        return response()->json($conversations);
    }

    /**
     * Store a newly created message
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'receiver_id' => 'required|exists:users,id',
            'property_id' => 'nullable|exists:properties,id',
            'message' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if trying to message themselves
        if ($request->receiver_id == Auth::id()) {
            return response()->json([
                'message' => 'Cannot send message to yourself'
            ], 400);
        }

        $message = Message::create([
            'sender_id' => Auth::id(),
            'receiver_id' => $request->receiver_id,
            'property_id' => $request->property_id,
            'message' => $request->message,
        ]);

        $message->load(['sender', 'receiver', 'property']);

        return response()->json([
            'message' => 'Message sent successfully',
            'data' => $message
        ], 201);
    }

    /**
     * Get conversation between two users for a specific property
     */
    public function show(Request $request, string $userId)
    {
        $currentUserId = Auth::id();
        $propertyId = $request->query('property_id');

        $query = Message::with(['sender', 'receiver'])
            ->betweenUsers($currentUserId, $userId);

        if ($propertyId) {
            $query->where('property_id', $propertyId);
        }

        $messages = $query->orderBy('created_at', 'asc')->get();

        // Mark messages as read
        Message::where('sender_id', $userId)
            ->where('receiver_id', $currentUserId)
            ->where('is_read', false)
            ->when($propertyId, function ($q) use ($propertyId) {
                $q->where('property_id', $propertyId);
            })
            ->update([
                'is_read' => true,
                'read_at' => now()
            ]);

        return response()->json($messages);
    }

    /**
     * Mark message as read
     */
    public function markAsRead(string $id)
    {
        $message = Message::findOrFail($id);

        // Check authorization
        if ($message->receiver_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $message->update([
            'is_read' => true,
            'read_at' => now()
        ]);

        return response()->json(['message' => 'Message marked as read']);
    }

    /**
     * Get unread message count
     */
    public function unreadCount()
    {
        $count = Message::where('receiver_id', Auth::id())
            ->where('is_read', false)
            ->count();

        return response()->json(['unread_count' => $count]);
    }

    /**
     * Delete a message
     */
    public function destroy(string $id)
    {
        $message = Message::findOrFail($id);

        // Check authorization - only sender can delete
        if ($message->sender_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $message->delete();

        return response()->json(['message' => 'Message deleted successfully']);
    }
}
