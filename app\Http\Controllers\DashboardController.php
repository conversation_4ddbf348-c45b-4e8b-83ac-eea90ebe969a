<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;

class DashboardController extends Controller
{
    /**
     * Handle the incoming request and redirect to appropriate dashboard
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Ensure user has roles loaded
        if (!$user->roles || $user->roles->isEmpty()) {
            // Assign default tenant role if no role is assigned
            $user->assignRole('tenant');
            $user->load('roles'); // Reload the roles relationship
        }

        // Redirect based on user role
        if ($user->hasRole('admin')) {
            return Redirect::route('admin.dashboard');
        } elseif ($user->hasRole('landlord') || $user->hasRole('agent')) {
            return Redirect::route('landlord.dashboard');
        } elseif ($user->hasRole('tenant')) {
            return Redirect::route('tenant.dashboard');
        }

        // Default fallback - shouldn't happen if roles are properly assigned
        return Redirect::route('tenant.dashboard');
    }
}
