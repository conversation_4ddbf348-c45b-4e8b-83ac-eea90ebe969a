import React from 'react';
import { Head } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { PageProps } from '@/types';
import {
    DocumentTextIcon,
    ChartBarIcon,
    UsersIcon,
    HomeIcon,
    CurrencyDollarIcon,
    CalendarIcon,
    ArrowDownTrayIcon,
    EyeIcon,
} from '@heroicons/react/24/outline';

export default function Reports({ auth }: PageProps) {
    const reportTypes = [
        {
            title: 'User Analytics Report',
            description: 'Comprehensive user registration, activity, and engagement metrics',
            icon: UsersIcon,
            color: 'bg-blue-100 text-blue-600',
            lastGenerated: '2 hours ago',
            frequency: 'Daily'
        },
        {
            title: 'Property Performance Report',
            description: 'Property views, bookings, and revenue performance analysis',
            icon: HomeIcon,
            color: 'bg-green-100 text-green-600',
            lastGenerated: '1 day ago',
            frequency: 'Weekly'
        },
        {
            title: 'Financial Summary Report',
            description: 'Revenue, payments, and financial transaction summaries',
            icon: CurrencyDollarIcon,
            color: 'bg-yellow-100 text-yellow-600',
            lastGenerated: '3 days ago',
            frequency: 'Monthly'
        },
        {
            title: 'Booking Analytics Report',
            description: 'Booking trends, cancellations, and occupancy rates',
            icon: CalendarIcon,
            color: 'bg-purple-100 text-purple-600',
            lastGenerated: '1 week ago',
            frequency: 'Weekly'
        },
        {
            title: 'Platform Usage Report',
            description: 'Overall platform usage, traffic, and performance metrics',
            icon: ChartBarIcon,
            color: 'bg-indigo-100 text-indigo-600',
            lastGenerated: '5 hours ago',
            frequency: 'Daily'
        }
    ];

    const quickStats = [
        { label: 'Total Reports Generated', value: '1,247', change: '+12%' },
        { label: 'Scheduled Reports', value: '23', change: '+3%' },
        { label: 'Data Sources', value: '8', change: '0%' },
        { label: 'Export Formats', value: '4', change: '0%' }
    ];

    return (
        <DashboardLayout
            user={auth.user}
            header={
                <div className="flex items-center justify-between w-full">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Reports</h1>
                        <p className="text-gray-600">Generate and view platform reports</p>
                    </div>
                    <div className="flex items-center space-x-4">
                        <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option>Last 30 days</option>
                            <option>Last 3 months</option>
                            <option>Last 6 months</option>
                            <option>Last year</option>
                        </select>
                        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center">
                            <DocumentTextIcon className="h-4 w-4 mr-2" />
                            Generate Custom Report
                        </button>
                    </div>
                </div>
            }
        >
            <Head title="Reports - Admin" />

            <div className="space-y-6">
                {/* Quick Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    {quickStats.map((stat, index) => (
                        <div key={index} className="bg-white rounded-lg shadow p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-500">{stat.label}</p>
                                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                                </div>
                                <div className="text-sm text-green-600 font-medium">{stat.change}</div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Report Types */}
                <div className="bg-white shadow rounded-lg">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-lg font-medium text-gray-900">Available Reports</h3>
                    </div>
                    <div className="p-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {reportTypes.map((report, index) => (
                                <div key={index} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                                    <div className="flex items-center mb-4">
                                        <div className={`p-3 rounded-lg ${report.color}`}>
                                            <report.icon className="h-6 w-6" />
                                        </div>
                                        <div className="ml-4">
                                            <h4 className="text-lg font-medium text-gray-900">{report.title}</h4>
                                            <p className="text-sm text-gray-500">{report.frequency}</p>
                                        </div>
                                    </div>
                                    <p className="text-gray-600 mb-4">{report.description}</p>
                                    <div className="flex items-center justify-between">
                                        <div className="text-sm text-gray-500">
                                            Last generated: {report.lastGenerated}
                                        </div>
                                    </div>
                                    <div className="mt-4 flex space-x-2">
                                        <button className="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 transition-colors flex items-center justify-center">
                                            <ArrowDownTrayIcon className="h-4 w-4 mr-1" />
                                            Generate
                                        </button>
                                        <button className="bg-gray-100 text-gray-700 px-3 py-2 rounded text-sm hover:bg-gray-200 transition-colors flex items-center justify-center">
                                            <EyeIcon className="h-4 w-4 mr-1" />
                                            View
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Recent Reports */}
                <div className="bg-white shadow rounded-lg">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-lg font-medium text-gray-900">Recent Reports</h3>
                    </div>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Report Name
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Type
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Generated
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Size
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                <tr className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        User Analytics - December 2024
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        User Analytics
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        2 hours ago
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        2.4 MB
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div className="flex space-x-2">
                                            <button className="text-blue-600 hover:text-blue-900">Download</button>
                                            <button className="text-green-600 hover:text-green-900">View</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        Property Performance - Week 50
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        Property Performance
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        1 day ago
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        1.8 MB
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div className="flex space-x-2">
                                            <button className="text-blue-600 hover:text-blue-900">Download</button>
                                            <button className="text-green-600 hover:text-green-900">View</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    );
}
