import React, { useState, useEffect } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, CalendarIcon } from '@heroicons/react/24/outline';
import { gsap } from 'gsap';

interface AvailabilityData {
    date: string;
    status: 'available' | 'booked' | 'blocked';
    price_override?: number;
    notes?: string;
}

interface AvailabilityCalendarProps {
    propertyId: number;
    basePrice: number;
    listingType: 'rent' | 'sale';
    availabilityData?: AvailabilityData[];
    onDateSelect?: (date: string) => void;
    onDateRangeSelect?: (startDate: string, endDate: string) => void;
    allowRangeSelection?: boolean;
    readOnly?: boolean;
}

const AvailabilityCalendar: React.FC<AvailabilityCalendarProps> = ({
    propertyId,
    basePrice,
    listingType,
    availabilityData = [],
    onDateSelect,
    onDateRangeSelect,
    allowRangeSelection = false,
    readOnly = false,
}) => {
    const [currentDate, setCurrentDate] = useState(new Date());
    const [selectedDate, setSelectedDate] = useState<string | null>(null);
    const [selectedRange, setSelectedRange] = useState<{ start: string | null; end: string | null }>({
        start: null,
        end: null,
    });
    const [hoveredDate, setHoveredDate] = useState<string | null>(null);

    useEffect(() => {
        // Animate calendar on load
        gsap.fromTo('.calendar-container',
            { scale: 0.95, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.5, ease: "power2.out" }
        );

        gsap.fromTo('.calendar-day',
            { scale: 0.8, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.3, stagger: 0.02, delay: 0.2, ease: "power2.out" }
        );
    }, [currentDate]);

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP',
            minimumFractionDigits: 0,
        }).format(price);
    };

    const formatDate = (date: Date) => {
        return date.toISOString().split('T')[0];
    };

    const getAvailabilityForDate = (dateString: string): AvailabilityData | null => {
        return availabilityData.find(item => item.date === dateString) || null;
    };

    const getDaysInMonth = (date: Date) => {
        const year = date.getFullYear();
        const month = date.getMonth();
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = firstDay.getDay();

        const days = [];

        // Add empty cells for days before the first day of the month
        for (let i = 0; i < startingDayOfWeek; i++) {
            days.push(null);
        }

        // Add days of the month
        for (let day = 1; day <= daysInMonth; day++) {
            days.push(new Date(year, month, day));
        }

        return days;
    };

    const navigateMonth = (direction: 'prev' | 'next') => {
        const newDate = new Date(currentDate);
        if (direction === 'prev') {
            newDate.setMonth(newDate.getMonth() - 1);
        } else {
            newDate.setMonth(newDate.getMonth() + 1);
        }
        setCurrentDate(newDate);
    };

    const handleDateClick = (date: Date) => {
        if (readOnly) return;

        const dateString = formatDate(date);
        const availability = getAvailabilityForDate(dateString);

        // Don't allow selection of booked dates
        if (availability?.status === 'booked') return;

        if (allowRangeSelection) {
            if (!selectedRange.start || (selectedRange.start && selectedRange.end)) {
                // Start new range
                setSelectedRange({ start: dateString, end: null });
            } else if (selectedRange.start && !selectedRange.end) {
                // Complete range
                const start = new Date(selectedRange.start);
                const end = date;
                
                if (end >= start) {
                    setSelectedRange({ start: selectedRange.start, end: dateString });
                    onDateRangeSelect?.(selectedRange.start, dateString);
                } else {
                    setSelectedRange({ start: dateString, end: null });
                }
            }
        } else {
            setSelectedDate(dateString);
            onDateSelect?.(dateString);
        }
    };

    const isDateInRange = (date: Date): boolean => {
        if (!allowRangeSelection || !selectedRange.start) return false;
        
        const dateString = formatDate(date);
        const start = selectedRange.start;
        const end = selectedRange.end || hoveredDate;
        
        if (!end) return dateString === start;
        
        return dateString >= start && dateString <= end;
    };

    const isDateRangeStart = (date: Date): boolean => {
        return allowRangeSelection && selectedRange.start === formatDate(date);
    };

    const isDateRangeEnd = (date: Date): boolean => {
        return allowRangeSelection && selectedRange.end === formatDate(date);
    };

    const getDateClasses = (date: Date): string => {
        const dateString = formatDate(date);
        const availability = getAvailabilityForDate(dateString);
        const isToday = formatDate(new Date()) === dateString;
        const isSelected = selectedDate === dateString;
        const inRange = isDateInRange(date);
        const rangeStart = isDateRangeStart(date);
        const rangeEnd = isDateRangeEnd(date);

        let classes = 'calendar-day relative w-10 h-10 flex items-center justify-center text-sm cursor-pointer transition-all duration-200 ';

        if (availability?.status === 'booked') {
            classes += 'bg-red-100 text-red-400 cursor-not-allowed line-through ';
        } else if (availability?.status === 'blocked') {
            classes += 'bg-gray-200 text-gray-400 cursor-not-allowed ';
        } else if (isSelected || rangeStart || rangeEnd) {
            classes += 'bg-blue-600 text-white font-semibold ';
        } else if (inRange) {
            classes += 'bg-blue-100 text-blue-700 ';
        } else if (isToday) {
            classes += 'bg-blue-50 text-blue-600 font-semibold border-2 border-blue-300 ';
        } else {
            classes += 'hover:bg-gray-100 text-gray-700 ';
        }

        if (rangeStart && !rangeEnd) {
            classes += 'rounded-l-full ';
        } else if (rangeEnd && !rangeStart) {
            classes += 'rounded-r-full ';
        } else if (rangeStart && rangeEnd) {
            classes += 'rounded-full ';
        } else if (inRange) {
            classes += 'rounded-none ';
        } else {
            classes += 'rounded-full ';
        }

        return classes;
    };

    const days = getDaysInMonth(currentDate);
    const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];
    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    return (
        <div className="calendar-container bg-white rounded-lg shadow-lg p-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
                <div>
                    <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                        <CalendarIcon className="h-5 w-5 mr-2" />
                        Availability Calendar
                    </h3>
                    {listingType === 'rent' && (
                        <p className="text-sm text-gray-600 mt-1">
                            Base price: {formatPrice(basePrice)}/month
                        </p>
                    )}
                </div>
                <div className="flex items-center space-x-2">
                    <button
                        onClick={() => navigateMonth('prev')}
                        className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                    >
                        <ChevronLeftIcon className="h-5 w-5" />
                    </button>
                    <h4 className="text-lg font-medium text-gray-900 min-w-[140px] text-center">
                        {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
                    </h4>
                    <button
                        onClick={() => navigateMonth('next')}
                        className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                    >
                        <ChevronRightIcon className="h-5 w-5" />
                    </button>
                </div>
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-1">
                {/* Day headers */}
                {dayNames.map((day) => (
                    <div key={day} className="h-10 flex items-center justify-center text-sm font-medium text-gray-500">
                        {day}
                    </div>
                ))}

                {/* Calendar days */}
                {days.map((date, index) => (
                    <div key={index} className="h-10 flex items-center justify-center">
                        {date ? (
                            <div
                                className={getDateClasses(date)}
                                onClick={() => handleDateClick(date)}
                                onMouseEnter={() => allowRangeSelection && setHoveredDate(formatDate(date))}
                                onMouseLeave={() => allowRangeSelection && setHoveredDate(null)}
                            >
                                {date.getDate()}
                                {/* Price override indicator */}
                                {getAvailabilityForDate(formatDate(date))?.price_override && (
                                    <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-green-500 rounded-full"></div>
                                )}
                            </div>
                        ) : (
                            <div className="w-10 h-10"></div>
                        )}
                    </div>
                ))}
            </div>

            {/* Legend */}
            <div className="mt-6 flex flex-wrap gap-4 text-sm">
                <div className="flex items-center">
                    <div className="w-4 h-4 bg-green-100 border border-green-300 rounded mr-2"></div>
                    <span className="text-gray-600">Available</span>
                </div>
                <div className="flex items-center">
                    <div className="w-4 h-4 bg-red-100 border border-red-300 rounded mr-2"></div>
                    <span className="text-gray-600">Booked</span>
                </div>
                <div className="flex items-center">
                    <div className="w-4 h-4 bg-gray-200 border border-gray-300 rounded mr-2"></div>
                    <span className="text-gray-600">Blocked</span>
                </div>
                <div className="flex items-center">
                    <div className="w-4 h-4 bg-blue-600 rounded mr-2"></div>
                    <span className="text-gray-600">Selected</span>
                </div>
            </div>

            {/* Selected range info */}
            {allowRangeSelection && selectedRange.start && selectedRange.end && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">
                        <strong>Selected period:</strong> {selectedRange.start} to {selectedRange.end}
                    </p>
                    {listingType === 'rent' && (
                        <p className="text-sm text-blue-600 mt-1">
                            Calculate total cost based on selected dates
                        </p>
                    )}
                </div>
            )}
        </div>
    );
};

export default AvailabilityCalendar;
