import React, { useState, useEffect, PropsWithChildren } from 'react';
import { Link, usePage } from '@inertiajs/react';
import { User } from '@/types';
import { gsap } from 'gsap';
import {
    HomeIcon,
    UsersIcon,
    BuildingOfficeIcon,
    CalendarIcon,
    CurrencyDollarIcon,
    ChatBubbleLeftRightIcon,
    StarIcon,
    Cog6ToothIcon,
    BellIcon,
    UserCircleIcon,
    ArrowRightOnRectangleIcon,
    Bars3Icon,
    XMarkIcon,
    ChartBarIcon,
    DocumentTextIcon,
    HeartIcon,
    ClipboardDocumentListIcon,
    MapPinIcon
} from '@heroicons/react/24/outline';

interface DashboardLayoutProps extends PropsWithChildren {
    user: User;
    header?: React.ReactNode;
}

interface MenuItem {
    name: string;
    href: string;
    icon: React.ComponentType<any>;
    current?: boolean;
    badge?: number;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ user, header, children }) => {
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const { url } = usePage();

    useEffect(() => {
        // Animate sidebar and content on load
        gsap.fromTo('.sidebar-item',
            { x: -20, opacity: 0 },
            { x: 0, opacity: 1, duration: 0.4, stagger: 0.05, ease: "power2.out" }
        );

        gsap.fromTo('.dashboard-content',
            { y: 20, opacity: 0 },
            { y: 0, opacity: 1, duration: 0.6, delay: 0.2, ease: "power2.out" }
        );
    }, []);

    const getMenuItems = (): MenuItem[] => {
        const baseItems: MenuItem[] = [
            { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
            { name: 'Properties', href: '/properties', icon: BuildingOfficeIcon },
            { name: 'Messages', href: '/messages', icon: ChatBubbleLeftRightIcon, badge: 3 },
            { name: 'Profile', href: '/profile', icon: UserCircleIcon },
        ];

        if (user.user_type === 'admin') {
            return [
                { name: 'Dashboard', href: '/admin/dashboard', icon: ChartBarIcon },
                { name: 'Users', href: '/admin/users', icon: UsersIcon },
                { name: 'Properties', href: '/admin/properties', icon: BuildingOfficeIcon },
                { name: 'Bookings', href: '/admin/bookings', icon: CalendarIcon },
                { name: 'Payments', href: '/admin/payments', icon: CurrencyDollarIcon },
                { name: 'Reviews', href: '/admin/reviews', icon: StarIcon, badge: 5 },
                { name: 'Reports', href: '/admin/reports', icon: DocumentTextIcon },
                { name: 'Settings', href: '/admin/settings', icon: Cog6ToothIcon },
            ];
        } else if (user.user_type === 'landlord') {
            return [
                { name: 'Dashboard', href: '/landlord/dashboard', icon: HomeIcon },
                { name: 'My Properties', href: '/landlord/properties', icon: BuildingOfficeIcon },
                { name: 'Bookings', href: '/landlord/bookings', icon: CalendarIcon, badge: 2 },
                { name: 'Payments', href: '/landlord/payments', icon: CurrencyDollarIcon },
                { name: 'Messages', href: '/landlord/messages', icon: ChatBubbleLeftRightIcon, badge: 4 },
                { name: 'Reviews', href: '/landlord/reviews', icon: StarIcon },
                { name: 'Analytics', href: '/landlord/analytics', icon: ChartBarIcon },
                { name: 'Profile', href: '/profile', icon: UserCircleIcon },
            ];
        } else {
            return [
                { name: 'Dashboard', href: '/tenant/dashboard', icon: HomeIcon },
                { name: 'Browse Properties', href: '/properties', icon: MapPinIcon },
                { name: 'My Bookings', href: '/tenant/bookings', icon: CalendarIcon },
                { name: 'Favorites', href: '/tenant/favorites', icon: HeartIcon },
                { name: 'Messages', href: '/tenant/messages', icon: ChatBubbleLeftRightIcon, badge: 1 },
                { name: 'Payment History', href: '/tenant/payments', icon: CurrencyDollarIcon },
                { name: 'My Reviews', href: '/tenant/reviews', icon: StarIcon },
                { name: 'Profile', href: '/profile', icon: UserCircleIcon },
            ];
        }
    };

    const menuItems = getMenuItems();

    const isCurrentPage = (href: string) => {
        return url === href || url.startsWith(href + '/');
    };

    return (
        <div className="min-h-screen bg-gray-50 flex">
            {/* Mobile sidebar overlay */}
            {sidebarOpen && (
                <div className="fixed inset-0 z-40 lg:hidden">
                    <div className="fixed inset-0 bg-black bg-opacity-50" onClick={() => setSidebarOpen(false)} />
                </div>
            )}

            {/* Sidebar */}
            <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl transform transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0 lg:shadow-none ${
                sidebarOpen ? 'translate-x-0' : '-translate-x-full'
            }`}>
                <div className="flex flex-col h-full">
                    {/* Logo */}
                    <div className="flex items-center justify-between h-16 px-6 bg-gradient-to-r from-blue-600 to-indigo-600">
                        <Link href="/" className="flex items-center group">
                            <HomeIcon className="h-8 w-8 text-white mr-2 group-hover:scale-110 transition-transform duration-300" />
                            <h1 className="text-xl font-bold text-white group-hover:text-blue-100 transition-colors duration-300">
                                PropertyHub
                            </h1>
                        </Link>
                        <button
                            onClick={() => setSidebarOpen(false)}
                            className="lg:hidden p-1 text-white hover:bg-white/20 rounded-md transition-colors"
                        >
                            <XMarkIcon className="h-6 w-6" />
                        </button>
                    </div>

                    {/* User Info */}
                    <div className="p-6 bg-gray-50 border-b border-gray-200">
                        <div className="flex items-center">
                            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                                <span className="text-white font-semibold text-sm">
                                    {user.name.charAt(0).toUpperCase()}
                                </span>
                            </div>
                            <div className="ml-3">
                                <p className="text-sm font-medium text-gray-900">{user.name}</p>
                                <p className="text-xs text-gray-500 capitalize">{user.user_type}</p>
                            </div>
                        </div>
                    </div>

                    {/* Navigation */}
                    <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto min-h-0">
                        {menuItems.map((item) => {
                            const Icon = item.icon;
                            const current = isCurrentPage(item.href);

                            return (
                                <Link
                                    key={item.name}
                                    href={item.href}
                                    className={`sidebar-item group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                                        current
                                            ? 'bg-blue-600 text-white shadow-md'
                                            : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600'
                                    }`}
                                >
                                    <Icon className={`mr-3 h-5 w-5 transition-colors duration-200 ${
                                        current ? 'text-white' : 'text-gray-400 group-hover:text-blue-600'
                                    }`} />
                                    <span className="flex-1">{item.name}</span>
                                    {item.badge && (
                                        <span className={`inline-flex items-center justify-center px-2 py-1 text-xs font-bold rounded-full ${
                                            current
                                                ? 'bg-white text-blue-600'
                                                : 'bg-red-100 text-red-800'
                                        }`}>
                                            {item.badge}
                                        </span>
                                    )}
                                </Link>
                            );
                        })}
                    </nav>

                    {/* Footer */}
                    <div className="p-4 border-t border-gray-200 flex-shrink-0">
                        <div className="space-y-2">
                            <Link
                                href="/notifications"
                                className="sidebar-item group flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                            >
                                <BellIcon className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-600" />
                                Notifications
                                <span className="ml-auto bg-red-100 text-red-800 text-xs font-bold px-2 py-1 rounded-full">
                                    3
                                </span>
                            </Link>
                            <Link
                                method="post"
                                href="/logout"
                                className="sidebar-item group flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-red-50 hover:text-red-600 transition-colors duration-200"
                            >
                                <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5 text-gray-400 group-hover:text-red-600" />
                                Sign Out
                            </Link>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main content */}
            <div className="flex-1 min-h-screen bg-gray-50 flex flex-col">
                {/* Top bar */}
                <div className="sticky top-0 z-30 bg-white shadow-sm border-b border-gray-200">
                    <div className="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center">
                            <button
                                onClick={() => setSidebarOpen(true)}
                                className="lg:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
                            >
                                <Bars3Icon className="h-6 w-6" />
                            </button>
                            {header && (
                                <div className="ml-4 lg:ml-0">
                                    {header}
                                </div>
                            )}
                        </div>
                        <div className="flex items-center space-x-4">
                            <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors relative">
                                <BellIcon className="h-6 w-6" />
                                <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                                    3
                                </span>
                            </button>
                            <Link
                                href="/profile"
                                className="flex items-center space-x-2 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                            >
                                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                    <span className="text-white font-semibold text-xs">
                                        {user.name.charAt(0).toUpperCase()}
                                    </span>
                                </div>
                                <span className="hidden md:block text-sm font-medium">{user.name}</span>
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Page content */}
                <main className="dashboard-content flex-1 overflow-auto">
                    <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
                        {children}
                    </div>
                </main>
            </div>
        </div>
    );
};

export default DashboardLayout;
