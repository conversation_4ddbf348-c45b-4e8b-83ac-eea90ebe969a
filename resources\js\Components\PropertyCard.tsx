import React from 'react';
import { Link } from '@inertiajs/react';
import { HeartIcon, MapPinIcon, HomeIcon, BanknotesIcon } from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';

interface Property {
    id: number;
    title: string;
    description: string;
    type: string;
    listing_type: string;
    price: number;
    bedrooms: number;
    bathrooms: number;
    area?: number;
    address: string;
    city: string;
    state: string;
    primary_image?: {
        image_path: string;
        alt_text?: string;
    };
    user: {
        name: string;
    };
    is_featured: boolean;
    created_at: string;
}

interface PropertyCardProps {
    property: Property;
    isFavorite?: boolean;
    onToggleFavorite?: (propertyId: number) => void;
    showFavoriteButton?: boolean;
}

const PropertyCard: React.FC<PropertyCardProps> = ({
    property,
    isFavorite = false,
    onToggleFavorite,
    showFavoriteButton = true,
}) => {
    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP',
            minimumFractionDigits: 0,
        }).format(price);
    };

    const getPropertyTypeIcon = (type: string) => {
        return <HomeIcon className="w-4 h-4" />;
    };

    const handleFavoriteClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (onToggleFavorite) {
            onToggleFavorite(property.id);
        }
    };

    return (
        <div className="card card-hover overflow-hidden group">
            <div className="relative">
                {/* Property Image */}
                <div className="aspect-w-16 aspect-h-9 bg-gray-200 overflow-hidden">
                    {property.primary_image ? (
                        <img
                            src={
                                property.primary_image.image_path.startsWith('http')
                                    ? property.primary_image.image_path
                                    : `/storage/${property.primary_image.image_path}`
                            }
                            alt={property.primary_image.alt_text || property.title}
                            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                    ) : (
                        <div className="w-full h-48 bg-gray-300 flex items-center justify-center">
                            <HomeIcon className="w-12 h-12 text-gray-400" />
                        </div>
                    )}
                </div>

                {/* Featured Badge */}
                {property.is_featured && (
                    <div className="absolute top-2 left-2 bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-md">
                        ⭐ Featured
                    </div>
                )}

                {/* Favorite Button */}
                {showFavoriteButton && (
                    <button
                        onClick={handleFavoriteClick}
                        className="absolute top-2 right-2 p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-md hover:bg-white hover:scale-110 transition-all duration-200"
                    >
                        {isFavorite ? (
                            <HeartIconSolid className="w-5 h-5 text-red-500" />
                        ) : (
                            <HeartIcon className="w-5 h-5 text-gray-600 hover:text-red-500" />
                        )}
                    </button>
                )}

                {/* Listing Type Badge */}
                <div className={`absolute bottom-2 left-2 px-3 py-1 rounded-full text-xs font-semibold text-white shadow-md ${
                    property.listing_type === 'rent'
                        ? 'bg-gradient-to-r from-green-500 to-green-600'
                        : 'bg-gradient-to-r from-blue-500 to-blue-600'
                }`}>
                    For {property.listing_type === 'rent' ? 'Rent' : 'Sale'}
                </div>

                {/* View Details Overlay */}
                <Link
                    href={`/properties/${property.id}`}
                    className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100"
                >
                    <div className="bg-white/90 backdrop-blur-sm px-4 py-2 rounded-lg font-medium text-gray-900 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
                        View Details
                    </div>
                </Link>
            </div>

            {/* Property Details */}
            <Link href={`/properties/${property.id}`} className="block p-4 hover:bg-gray-50 transition-colors duration-200">
                <div className="flex items-start justify-between mb-2">
                    <h3 className="text-lg font-semibold text-gray-900 truncate group-hover:text-blue-600 transition-colors duration-200">
                        {property.title}
                    </h3>
                    <div className="flex items-center text-gray-500 ml-2">
                        {getPropertyTypeIcon(property.type)}
                        <span className="ml-1 text-sm capitalize">{property.type}</span>
                    </div>
                </div>

                <div className="flex items-center text-gray-600 mb-3">
                    <MapPinIcon className="w-4 h-4 mr-1 flex-shrink-0" />
                    <span className="text-sm truncate">{property.address}, {property.city}</span>
                </div>

                <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3 text-sm text-gray-600">
                        <div className="flex items-center">
                            <span className="font-medium">{property.bedrooms}</span>
                            <span className="ml-1">bed{property.bedrooms !== 1 ? 's' : ''}</span>
                        </div>
                        <div className="flex items-center">
                            <span className="font-medium">{property.bathrooms}</span>
                            <span className="ml-1">bath{property.bathrooms !== 1 ? 's' : ''}</span>
                        </div>
                        {property.area && (
                            <div className="flex items-center">
                                <span className="font-medium">{property.area}</span>
                                <span className="ml-1">m²</span>
                            </div>
                        )}
                    </div>
                </div>

                <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                        <BanknotesIcon className="w-5 h-5 text-green-600 mr-1" />
                        <span className="text-xl font-bold text-green-600">
                            {formatPrice(property.price)}
                            {property.listing_type === 'rent' && <span className="text-sm font-normal text-gray-500">/month</span>}
                        </span>
                    </div>
                </div>

                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                        by {property.user.name}
                    </span>
                    <span className="text-xs text-blue-600 font-medium group-hover:text-blue-700">
                        View Details →
                    </span>
                </div>

                <p className="text-gray-600 text-sm mt-2 line-clamp-2">
                    {property.description}
                </p>
            </Link>
        </div>
    );
};

export default PropertyCard;
