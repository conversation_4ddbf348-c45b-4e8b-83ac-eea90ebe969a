export interface Role {
    id: number;
    name: string;
    guard_name: string;
}

export interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at?: string;
    user_type?: string;
    phone?: string;
    bio?: string;
    avatar?: string;
    is_verified?: boolean;
    is_active?: boolean;
    roles?: Role[];
}

export type PageProps<
    T extends Record<string, unknown> = Record<string, unknown>,
> = T & {
    auth: {
        user: User;
    };
};
