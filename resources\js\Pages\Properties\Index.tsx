import React from 'react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import GuestLayout from '@/Layouts/GuestLayout';
import PropertyList from '@/Components/PropertyList';
import { Head } from '@inertiajs/react';
import { PageProps } from '@/types';

interface Property {
    id: number;
    title: string;
    description: string;
    type: string;
    listing_type: string;
    price: number;
    bedrooms: number;
    bathrooms: number;
    area?: number;
    address: string;
    city: string;
    state: string;
    primary_image?: {
        image_path: string;
        alt_text?: string;
    };
    user: {
        name: string;
    };
    is_featured: boolean;
    created_at: string;
}

interface PaginationData {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    data: Property[];
}

interface PropertiesIndexProps extends PageProps {
    properties: PaginationData;
    filters?: {
        search?: string;
        type?: string;
        listing_type?: string;
        min_price?: number;
        max_price?: number;
        bedrooms?: number;
        bathrooms?: number;
        city?: string;
        featured?: boolean;
    };
}

export default function PropertiesIndex({ auth, properties, filters }: PropertiesIndexProps) {
    const Layout = auth.user ? AuthenticatedLayout : GuestLayout;
    const layoutProps = auth.user
        ? {
            header: (
                <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                    Property Listings
                </h2>
            )
        }
        : { fullWidth: true };

    return (
        <Layout {...layoutProps}>
            <Head title="Properties" />

            <div className="min-h-screen bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <div className="mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                            Find Your Perfect Property
                        </h1>
                        <p className="text-gray-600">
                            Discover amazing properties for rent and sale in the Philippines
                        </p>
                    </div>

                    <PropertyList
                        initialProperties={properties}
                        showFilters={true}
                        title=""
                    />
                </div>
            </div>
        </Layout>
    );
}
