<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Property;
use App\Models\PropertyImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class PropertyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Property::with(['user', 'primaryImage', 'images'])
            ->available();

        // Search filters
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('address', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }

        // Type filter
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Listing type filter
        if ($request->has('listing_type')) {
            $query->where('listing_type', $request->listing_type);
        }

        // Price range filter
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Bedrooms filter
        if ($request->has('bedrooms')) {
            $query->where('bedrooms', '>=', $request->bedrooms);
        }

        // Bathrooms filter
        if ($request->has('bathrooms')) {
            $query->where('bathrooms', '>=', $request->bathrooms);
        }

        // Location filter
        if ($request->has('city')) {
            $query->where('city', 'like', "%{$request->city}%");
        }

        // Featured properties
        if ($request->has('featured') && $request->featured) {
            $query->featured();
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $properties = $query->paginate($request->get('per_page', 12));

        return response()->json($properties);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'type' => 'required|in:apartment,house,condo,townhouse,studio,commercial',
            'listing_type' => 'required|in:rent,sale',
            'price' => 'required|numeric|min:0',
            'bedrooms' => 'required|integer|min:0',
            'bathrooms' => 'required|integer|min:0',
            'area' => 'nullable|numeric|min:0',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'postal_code' => 'required|string|max:20',
            'country' => 'nullable|string|max:100',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'amenities' => 'nullable|array',
            'features' => 'nullable|array',
            'available_from' => 'nullable|date',
            'video_url' => 'nullable|url',
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $property = Property::create([
            'user_id' => Auth::id(),
            'title' => $request->title,
            'description' => $request->description,
            'type' => $request->type,
            'listing_type' => $request->listing_type,
            'price' => $request->price,
            'bedrooms' => $request->bedrooms,
            'bathrooms' => $request->bathrooms,
            'area' => $request->area,
            'address' => $request->address,
            'city' => $request->city,
            'state' => $request->state,
            'postal_code' => $request->postal_code,
            'country' => $request->country ?? 'Philippines',
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'amenities' => $request->amenities,
            'features' => $request->features,
            'available_from' => $request->available_from,
            'video_url' => $request->video_url,
            'status' => 'draft',
        ]);

        // Handle image uploads
        if ($request->hasFile('images')) {
            $this->uploadImages($property, $request->file('images'));
        }

        return response()->json([
            'message' => 'Property created successfully',
            'property' => $property->load(['images', 'user'])
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $property = Property::with(['user', 'images', 'reviews.user', 'favorites'])
            ->findOrFail($id);

        // Increment views
        $property->incrementViews();

        return response()->json($property);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $property = Property::findOrFail($id);

        // Check if user owns the property
        if ($property->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'description' => 'sometimes|required|string',
            'type' => 'sometimes|required|in:apartment,house,condo,townhouse,studio,commercial',
            'listing_type' => 'sometimes|required|in:rent,sale',
            'price' => 'sometimes|required|numeric|min:0',
            'bedrooms' => 'sometimes|required|integer|min:0',
            'bathrooms' => 'sometimes|required|integer|min:0',
            'area' => 'nullable|numeric|min:0',
            'address' => 'sometimes|required|string|max:255',
            'city' => 'sometimes|required|string|max:100',
            'state' => 'sometimes|required|string|max:100',
            'postal_code' => 'sometimes|required|string|max:20',
            'country' => 'nullable|string|max:100',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'amenities' => 'nullable|array',
            'features' => 'nullable|array',
            'available_from' => 'nullable|date',
            'video_url' => 'nullable|url',
            'status' => 'sometimes|in:draft,published,rented,sold,inactive',
            'is_featured' => 'sometimes|boolean',
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $property->update($request->only([
            'title', 'description', 'type', 'listing_type', 'price',
            'bedrooms', 'bathrooms', 'area', 'address', 'city', 'state',
            'postal_code', 'country', 'latitude', 'longitude', 'amenities',
            'features', 'available_from', 'video_url', 'status', 'is_featured'
        ]));

        // Handle new image uploads
        if ($request->hasFile('images')) {
            $this->uploadImages($property, $request->file('images'));
        }

        return response()->json([
            'message' => 'Property updated successfully',
            'property' => $property->load(['images', 'user'])
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $property = Property::findOrFail($id);

        // Check if user owns the property
        if ($property->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Delete associated images
        foreach ($property->images as $image) {
            Storage::delete('public/' . $image->image_path);
        }

        $property->delete();

        return response()->json(['message' => 'Property deleted successfully']);
    }

    /**
     * Upload and process property images
     */
    private function uploadImages(Property $property, array $images)
    {
        $manager = new ImageManager(new Driver());

        foreach ($images as $index => $image) {
            $filename = time() . '_' . $index . '.' . $image->getClientOriginalExtension();
            $path = 'properties/' . $property->id . '/' . $filename;

            // Resize and optimize image
            $processedImage = $manager->read($image->getPathname())
                ->resize(1200, 800, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });

            Storage::put('public/' . $path, $processedImage->encode());

            PropertyImage::create([
                'property_id' => $property->id,
                'image_path' => $path,
                'sort_order' => $index,
                'is_primary' => $index === 0,
            ]);
        }
    }

    /**
     * Get featured properties
     */
    public function featured()
    {
        $properties = Property::with(['user', 'primaryImage'])
            ->available()
            ->featured()
            ->limit(6)
            ->get();

        return response()->json($properties);
    }

    /**
     * Get user's properties
     */
    public function myProperties()
    {
        $properties = Property::with(['images', 'bookings'])
            ->where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json($properties);
    }
}
