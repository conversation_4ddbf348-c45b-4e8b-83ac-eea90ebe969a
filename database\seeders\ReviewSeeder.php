<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Property;
use App\Models\Review;
use App\Models\User;

class ReviewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $properties = Property::all();
        $tenants = User::where('user_type', 'tenant')->get();

        if ($tenants->isEmpty()) {
            // Create some tenant users for reviews
            $sampleTenants = [
                [
                    'name' => '<PERSON>',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'user_type' => 'tenant',
                    'is_verified' => true,
                ],
                [
                    'name' => '<PERSON>',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'user_type' => 'tenant',
                    'is_verified' => true,
                ],
                [
                    'name' => '<PERSON>',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'user_type' => 'tenant',
                    'is_verified' => true,
                ],
            ];

            foreach ($sampleTenants as $tenantData) {
                $tenant = User::create($tenantData);
                $tenant->assignRole('tenant');
                $tenants->push($tenant);
            }
        }

        $sampleReviews = [
            [
                'rating' => 5,
                'comment' => 'Excellent property! The location is perfect and the amenities are top-notch. The landlord was very responsive and helpful throughout the process.',
            ],
            [
                'rating' => 4,
                'comment' => 'Great place to live. Very clean and well-maintained. The only minor issue is the parking space is a bit tight, but overall very satisfied.',
            ],
            [
                'rating' => 5,
                'comment' => 'Amazing property with beautiful views. The building facilities are excellent and the security is very good. Highly recommended!',
            ],
            [
                'rating' => 4,
                'comment' => 'Good value for money. The unit is spacious and the location is convenient. The management is professional and responsive.',
            ],
            [
                'rating' => 5,
                'comment' => 'Perfect for families! The neighborhood is safe and quiet, and there are good schools nearby. The property is exactly as described.',
            ],
            [
                'rating' => 3,
                'comment' => 'Decent property but could use some updates. The location is good and the price is fair. The landlord is understanding and easy to work with.',
            ],
        ];

        foreach ($properties as $property) {
            // Add 1-3 reviews per property
            $reviewCount = rand(1, 3);
            $selectedReviews = array_slice($sampleReviews, 0, $reviewCount);
            $usedTenants = [];

            foreach ($selectedReviews as $reviewData) {
                // Get a tenant that hasn't reviewed this property yet
                $availableTenants = $tenants->filter(function ($tenant) use ($usedTenants) {
                    return !in_array($tenant->id, $usedTenants);
                });

                if ($availableTenants->isEmpty()) {
                    break; // No more available tenants for this property
                }

                $selectedTenant = $availableTenants->random();
                $usedTenants[] = $selectedTenant->id;

                Review::create([
                    'property_id' => $property->id,
                    'user_id' => $selectedTenant->id,
                    'rating' => $reviewData['rating'],
                    'comment' => $reviewData['comment'],
                    'is_approved' => true,
                    'created_at' => now()->subDays(rand(1, 30)),
                ]);
            }
        }
    }
}
