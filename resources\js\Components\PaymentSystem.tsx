import React, { useState, useEffect } from 'react';
import { 
    CreditCardIcon, 
    BanknotesIcon, 
    ShieldCheckIcon,
    CheckCircleIcon,
    XMarkIcon,
    ClockIcon,
    ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { gsap } from 'gsap';

interface PaymentMethod {
    id: string;
    name: string;
    type: 'card' | 'bank' | 'ewallet' | 'cod';
    icon: string;
    description: string;
    fee?: number;
    processing_time: string;
    available: boolean;
}

interface PaymentSystemProps {
    amount: number;
    propertyTitle: string;
    paymentType: 'booking_fee' | 'rent' | 'deposit' | 'purchase';
    onPaymentSuccess: (paymentData: any) => void;
    onPaymentCancel: () => void;
    onClose: () => void;
}

const PaymentSystem: React.FC<PaymentSystemProps> = ({
    amount,
    propertyTitle,
    paymentType,
    onPaymentSuccess,
    onPaymentCancel,
    onClose,
}) => {
    const [selectedMethod, setSelectedMethod] = useState<string>('');
    const [isProcessing, setIsProcessing] = useState(false);
    const [paymentStep, setPaymentStep] = useState<'select' | 'details' | 'processing' | 'success' | 'error'>('select');
    const [paymentData, setPaymentData] = useState({
        cardNumber: '',
        expiryDate: '',
        cvv: '',
        cardName: '',
        email: '',
        phone: '',
    });

    useEffect(() => {
        // Animate payment modal
        gsap.fromTo('.payment-modal',
            { scale: 0.9, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.4, ease: "power2.out" }
        );

        gsap.fromTo('.payment-method',
            { y: 20, opacity: 0 },
            { y: 0, opacity: 1, duration: 0.3, stagger: 0.1, delay: 0.2, ease: "power2.out" }
        );
    }, []);

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP',
            minimumFractionDigits: 0,
        }).format(amount);
    };

    const paymentMethods: PaymentMethod[] = [
        {
            id: 'xendit_card',
            name: 'Credit/Debit Card',
            type: 'card',
            icon: '💳',
            description: 'Visa, Mastercard, JCB',
            fee: amount * 0.035, // 3.5% fee
            processing_time: 'Instant',
            available: true,
        },
        {
            id: 'xendit_bank',
            name: 'Bank Transfer',
            type: 'bank',
            icon: '🏦',
            description: 'BPI, BDO, Metrobank, and more',
            fee: 15,
            processing_time: '1-3 business days',
            available: true,
        },
        {
            id: 'xendit_gcash',
            name: 'GCash',
            type: 'ewallet',
            icon: '📱',
            description: 'Pay with your GCash wallet',
            fee: 0,
            processing_time: 'Instant',
            available: true,
        },
        {
            id: 'xendit_paymaya',
            name: 'PayMaya',
            type: 'ewallet',
            icon: '💰',
            description: 'Pay with your PayMaya wallet',
            fee: 0,
            processing_time: 'Instant',
            available: true,
        },
        {
            id: 'cod',
            name: 'Cash on Delivery',
            type: 'cod',
            icon: '💵',
            description: 'Pay when you meet the landlord',
            fee: 0,
            processing_time: 'On meeting',
            available: paymentType === 'booking_fee',
        },
    ];

    const handleMethodSelect = (methodId: string) => {
        setSelectedMethod(methodId);
        if (methodId === 'cod') {
            setPaymentStep('processing');
            handleCODPayment();
        } else {
            setPaymentStep('details');
        }
    };

    const handleCODPayment = async () => {
        setIsProcessing(true);
        
        // Simulate API call
        setTimeout(() => {
            setIsProcessing(false);
            setPaymentStep('success');
            onPaymentSuccess({
                method: 'cod',
                amount: amount,
                status: 'pending',
                reference: `COD-${Date.now()}`,
            });
        }, 2000);
    };

    const handleXenditPayment = async () => {
        setIsProcessing(true);
        setPaymentStep('processing');

        try {
            // In a real implementation, you would integrate with Xendit API
            // This is a simulation
            const response = await simulateXenditPayment();
            
            if (response.success) {
                setPaymentStep('success');
                onPaymentSuccess(response.data);
            } else {
                setPaymentStep('error');
            }
        } catch (error) {
            setPaymentStep('error');
        } finally {
            setIsProcessing(false);
        }
    };

    const simulateXenditPayment = (): Promise<any> => {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: true,
                    data: {
                        method: selectedMethod,
                        amount: amount,
                        status: 'completed',
                        reference: `XEN-${Date.now()}`,
                        transaction_id: `txn_${Math.random().toString(36).substr(2, 9)}`,
                    }
                });
            }, 3000);
        });
    };

    const selectedMethodData = paymentMethods.find(m => m.id === selectedMethod);
    const totalAmount = selectedMethodData ? amount + (selectedMethodData.fee || 0) : amount;

    const getPaymentTypeLabel = () => {
        switch (paymentType) {
            case 'booking_fee': return 'Booking Fee';
            case 'rent': return 'Monthly Rent';
            case 'deposit': return 'Security Deposit';
            case 'purchase': return 'Property Purchase';
            default: return 'Payment';
        }
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="payment-modal bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-auto">
                {/* Header */}
                <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6 rounded-t-lg">
                    <div className="flex items-center justify-between">
                        <div>
                            <h2 className="text-xl font-semibold">Secure Payment</h2>
                            <p className="text-blue-100 mt-1">{getPaymentTypeLabel()} for {propertyTitle}</p>
                        </div>
                        <button
                            onClick={onClose}
                            className="p-2 hover:bg-white/20 rounded-full transition-colors"
                        >
                            <XMarkIcon className="h-6 w-6" />
                        </button>
                    </div>
                </div>

                <div className="p-6">
                    {/* Payment Amount */}
                    <div className="bg-gray-50 rounded-lg p-4 mb-6">
                        <div className="flex items-center justify-between">
                            <span className="text-gray-600">Amount to Pay:</span>
                            <span className="text-2xl font-bold text-gray-900">{formatCurrency(totalAmount)}</span>
                        </div>
                        {selectedMethodData?.fee && selectedMethodData.fee > 0 && (
                            <div className="flex items-center justify-between text-sm text-gray-500 mt-2">
                                <span>Processing Fee:</span>
                                <span>{formatCurrency(selectedMethodData.fee)}</span>
                            </div>
                        )}
                    </div>

                    {/* Payment Steps */}
                    {paymentStep === 'select' && (
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Choose Payment Method</h3>
                            <div className="space-y-3">
                                {paymentMethods.filter(method => method.available).map((method) => (
                                    <button
                                        key={method.id}
                                        onClick={() => handleMethodSelect(method.id)}
                                        className="payment-method w-full p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 text-left"
                                    >
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center space-x-3">
                                                <span className="text-2xl">{method.icon}</span>
                                                <div>
                                                    <div className="font-medium text-gray-900">{method.name}</div>
                                                    <div className="text-sm text-gray-500">{method.description}</div>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <div className="text-sm text-gray-600">{method.processing_time}</div>
                                                {method.fee && method.fee > 0 && (
                                                    <div className="text-xs text-gray-500">+{formatCurrency(method.fee)} fee</div>
                                                )}
                                            </div>
                                        </div>
                                    </button>
                                ))}
                            </div>
                        </div>
                    )}

                    {paymentStep === 'details' && selectedMethodData && (
                        <div>
                            <div className="flex items-center mb-4">
                                <button
                                    onClick={() => setPaymentStep('select')}
                                    className="text-blue-600 hover:text-blue-800 mr-3"
                                >
                                    ← Back
                                </button>
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Pay with {selectedMethodData.name}
                                </h3>
                            </div>

                            {selectedMethodData.type === 'card' && (
                                <form onSubmit={(e) => { e.preventDefault(); handleXenditPayment(); }} className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div className="md:col-span-2">
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Card Number
                                            </label>
                                            <input
                                                type="text"
                                                placeholder="1234 5678 9012 3456"
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                                value={paymentData.cardNumber}
                                                onChange={(e) => setPaymentData({...paymentData, cardNumber: e.target.value})}
                                                required
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Expiry Date
                                            </label>
                                            <input
                                                type="text"
                                                placeholder="MM/YY"
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                                value={paymentData.expiryDate}
                                                onChange={(e) => setPaymentData({...paymentData, expiryDate: e.target.value})}
                                                required
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                CVV
                                            </label>
                                            <input
                                                type="text"
                                                placeholder="123"
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                                value={paymentData.cvv}
                                                onChange={(e) => setPaymentData({...paymentData, cvv: e.target.value})}
                                                required
                                            />
                                        </div>
                                        <div className="md:col-span-2">
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Cardholder Name
                                            </label>
                                            <input
                                                type="text"
                                                placeholder="John Doe"
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                                value={paymentData.cardName}
                                                onChange={(e) => setPaymentData({...paymentData, cardName: e.target.value})}
                                                required
                                            />
                                        </div>
                                    </div>
                                    <button
                                        type="submit"
                                        disabled={isProcessing}
                                        className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                                    >
                                        <ShieldCheckIcon className="h-5 w-5 mr-2" />
                                        Pay {formatCurrency(totalAmount)}
                                    </button>
                                </form>
                            )}

                            {(selectedMethodData.type === 'ewallet' || selectedMethodData.type === 'bank') && (
                                <div className="text-center py-8">
                                    <div className="text-6xl mb-4">{selectedMethodData.icon}</div>
                                    <h4 className="text-lg font-medium text-gray-900 mb-2">
                                        Pay with {selectedMethodData.name}
                                    </h4>
                                    <p className="text-gray-600 mb-6">
                                        You will be redirected to {selectedMethodData.name} to complete your payment
                                    </p>
                                    <button
                                        onClick={handleXenditPayment}
                                        disabled={isProcessing}
                                        className="bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        Continue to {selectedMethodData.name}
                                    </button>
                                </div>
                            )}
                        </div>
                    )}

                    {paymentStep === 'processing' && (
                        <div className="text-center py-12">
                            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">Processing Payment</h3>
                            <p className="text-gray-600">Please wait while we process your payment...</p>
                        </div>
                    )}

                    {paymentStep === 'success' && (
                        <div className="text-center py-12">
                            <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-4" />
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">Payment Successful!</h3>
                            <p className="text-gray-600 mb-6">
                                Your payment of {formatCurrency(totalAmount)} has been processed successfully.
                            </p>
                            <button
                                onClick={onClose}
                                className="bg-green-600 text-white py-2 px-6 rounded-lg hover:bg-green-700 transition-colors"
                            >
                                Continue
                            </button>
                        </div>
                    )}

                    {paymentStep === 'error' && (
                        <div className="text-center py-12">
                            <ExclamationTriangleIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">Payment Failed</h3>
                            <p className="text-gray-600 mb-6">
                                There was an error processing your payment. Please try again.
                            </p>
                            <div className="flex space-x-3 justify-center">
                                <button
                                    onClick={() => setPaymentStep('select')}
                                    className="bg-gray-600 text-white py-2 px-6 rounded-lg hover:bg-gray-700 transition-colors"
                                >
                                    Try Again
                                </button>
                                <button
                                    onClick={onPaymentCancel}
                                    className="bg-red-600 text-white py-2 px-6 rounded-lg hover:bg-red-700 transition-colors"
                                >
                                    Cancel
                                </button>
                            </div>
                        </div>
                    )}

                    {/* Security Notice */}
                    {(paymentStep === 'select' || paymentStep === 'details') && (
                        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                            <div className="flex items-center">
                                <ShieldCheckIcon className="h-5 w-5 text-green-600 mr-2" />
                                <span className="text-sm text-green-800">
                                    Your payment is secured by 256-bit SSL encryption and processed by Xendit
                                </span>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default PaymentSystem;
