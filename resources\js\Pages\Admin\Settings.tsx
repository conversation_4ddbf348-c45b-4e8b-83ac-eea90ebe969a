import React, { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { PageProps } from '@/types';
import {
    Cog6ToothIcon,
    GlobeAltIcon,
    ShieldCheckIcon,
    CurrencyDollarIcon,
    EnvelopeIcon,
    BellIcon,
    UserGroupIcon,
    DocumentTextIcon,
    CheckIcon,
} from '@heroicons/react/24/outline';

interface SettingsPageProps extends PageProps {
    flash?: {
        success?: string;
        error?: string;
    };
}

export default function Settings({ auth, flash }: SettingsPageProps) {
    const [activeTab, setActiveTab] = useState('general');

    const { data, setData, patch, processing, errors, reset } = useForm({
        platform_name: 'PropertyHub',
        platform_url: 'https://propertyhub.com',
        contact_email: '<EMAIL>',
        support_phone: '+****************',
        default_currency: 'USD',
        default_language: 'en',
        timezone: 'UTC',
    });

    const handleSubmit = () => {
        console.log('Submitting form with data:', data);

        patch(route('admin.settings.update'), {
            onSuccess: () => {
                console.log('Settings updated successfully!');
            },
            onError: (errors) => {
                console.log('Error updating settings:', errors);
            },
            onStart: () => {
                console.log('Request started');
            },
            onFinish: () => {
                console.log('Request finished');
            }
        });
    };

    const tabs = [
        { id: 'general', name: 'General', icon: Cog6ToothIcon },
        { id: 'security', name: 'Security', icon: ShieldCheckIcon },
        { id: 'payments', name: 'Payments', icon: CurrencyDollarIcon },
        { id: 'notifications', name: 'Notifications', icon: BellIcon },
        { id: 'users', name: 'User Management', icon: UserGroupIcon },
        { id: 'legal', name: 'Legal', icon: DocumentTextIcon },
    ];

    const renderGeneralSettings = () => (
        <div className="space-y-6">
            <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Platform Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Platform Name</label>
                        <input
                            type="text"
                            value={data.platform_name}
                            onChange={(e) => setData('platform_name', e.target.value)}
                            className={`w-full border rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                errors.platform_name ? 'border-red-300' : 'border-gray-300'
                            }`}
                        />
                        {errors.platform_name && (
                            <p className="mt-1 text-sm text-red-600">{errors.platform_name}</p>
                        )}
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Platform URL</label>
                        <input
                            type="url"
                            value={data.platform_url}
                            onChange={(e) => setData('platform_url', e.target.value)}
                            className={`w-full border rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                errors.platform_url ? 'border-red-300' : 'border-gray-300'
                            }`}
                        />
                        {errors.platform_url && (
                            <p className="mt-1 text-sm text-red-600">{errors.platform_url}</p>
                        )}
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                        <input
                            type="email"
                            value={data.contact_email}
                            onChange={(e) => setData('contact_email', e.target.value)}
                            className={`w-full border rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                errors.contact_email ? 'border-red-300' : 'border-gray-300'
                            }`}
                        />
                        {errors.contact_email && (
                            <p className="mt-1 text-sm text-red-600">{errors.contact_email}</p>
                        )}
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Support Phone</label>
                        <input
                            type="tel"
                            value={data.support_phone}
                            onChange={(e) => setData('support_phone', e.target.value)}
                            className={`w-full border rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                errors.support_phone ? 'border-red-300' : 'border-gray-300'
                            }`}
                        />
                        {errors.support_phone && (
                            <p className="mt-1 text-sm text-red-600">{errors.support_phone}</p>
                        )}
                    </div>
                </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Regional Settings</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Default Currency</label>
                        <select
                            value={data.default_currency}
                            onChange={(e) => setData('default_currency', e.target.value)}
                            className={`w-full border rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                errors.default_currency ? 'border-red-300' : 'border-gray-300'
                            }`}
                        >
                            <option value="USD">USD - US Dollar</option>
                            <option value="EUR">EUR - Euro</option>
                            <option value="GBP">GBP - British Pound</option>
                            <option value="PHP">PHP - Philippine Peso</option>
                        </select>
                        {errors.default_currency && (
                            <p className="mt-1 text-sm text-red-600">{errors.default_currency}</p>
                        )}
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Default Language</label>
                        <select
                            value={data.default_language}
                            onChange={(e) => setData('default_language', e.target.value)}
                            className={`w-full border rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                errors.default_language ? 'border-red-300' : 'border-gray-300'
                            }`}
                        >
                            <option value="en">English</option>
                            <option value="es">Spanish</option>
                            <option value="fr">French</option>
                            <option value="de">German</option>
                        </select>
                        {errors.default_language && (
                            <p className="mt-1 text-sm text-red-600">{errors.default_language}</p>
                        )}
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                        <select
                            value={data.timezone}
                            onChange={(e) => setData('timezone', e.target.value)}
                            className={`w-full border rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                errors.timezone ? 'border-red-300' : 'border-gray-300'
                            }`}
                        >
                            <option value="UTC">UTC</option>
                            <option value="America/New_York">Eastern Time</option>
                            <option value="America/Los_Angeles">Pacific Time</option>
                            <option value="Asia/Manila">Philippine Time</option>
                        </select>
                        {errors.timezone && (
                            <p className="mt-1 text-sm text-red-600">{errors.timezone}</p>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );

    const renderSecuritySettings = () => (
        <div className="space-y-6">
            <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Authentication Settings</h3>
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <h4 className="text-sm font-medium text-gray-900">Two-Factor Authentication</h4>
                            <p className="text-sm text-gray-500">Require 2FA for admin accounts</p>
                        </div>
                        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">Enable</button>
                    </div>
                    <div className="flex items-center justify-between">
                        <div>
                            <h4 className="text-sm font-medium text-gray-900">Password Requirements</h4>
                            <p className="text-sm text-gray-500">Minimum 8 characters, special characters required</p>
                        </div>
                        <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm">Configure</button>
                    </div>
                    <div className="flex items-center justify-between">
                        <div>
                            <h4 className="text-sm font-medium text-gray-900">Session Timeout</h4>
                            <p className="text-sm text-gray-500">Auto-logout after 30 minutes of inactivity</p>
                        </div>
                        <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm">Edit</button>
                    </div>
                </div>
            </div>
        </div>
    );

    const renderPaymentSettings = () => (
        <div className="space-y-6">
            <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Gateway Configuration</h3>
                <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div className="flex items-center">
                            <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <CurrencyDollarIcon className="h-6 w-6 text-blue-600" />
                            </div>
                            <div>
                                <h4 className="text-sm font-medium text-gray-900">Xendit</h4>
                                <p className="text-sm text-gray-500">Primary payment processor</p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-2">
                            <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                <CheckIcon className="h-3 w-3 mr-1" />
                                Active
                            </span>
                            <button className="bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm">Configure</button>
                        </div>
                    </div>
                    <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div className="flex items-center">
                            <div className="h-10 w-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                                <CurrencyDollarIcon className="h-6 w-6 text-gray-400" />
                            </div>
                            <div>
                                <h4 className="text-sm font-medium text-gray-900">Cash on Delivery</h4>
                                <p className="text-sm text-gray-500">Manual payment option</p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-2">
                            <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                <CheckIcon className="h-3 w-3 mr-1" />
                                Active
                            </span>
                            <button className="bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm">Configure</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );

    const renderContent = () => {
        switch (activeTab) {
            case 'general':
                return renderGeneralSettings();
            case 'security':
                return renderSecuritySettings();
            case 'payments':
                return renderPaymentSettings();
            case 'notifications':
                return (
                    <div className="bg-white shadow rounded-lg p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Settings</h3>
                        <p className="text-gray-600">Configure platform notifications and alerts.</p>
                    </div>
                );
            case 'users':
                return (
                    <div className="bg-white shadow rounded-lg p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">User Management Settings</h3>
                        <p className="text-gray-600">Configure user registration, roles, and permissions.</p>
                    </div>
                );
            case 'legal':
                return (
                    <div className="bg-white shadow rounded-lg p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Legal Settings</h3>
                        <p className="text-gray-600">Manage terms of service, privacy policy, and legal documents.</p>
                    </div>
                );
            default:
                return renderGeneralSettings();
        }
    };

    return (
        <DashboardLayout
            user={auth.user}
            header={
                <div className="flex items-center justify-between w-full">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
                        <p className="text-gray-600">Manage platform settings and configuration</p>
                    </div>
                    <button
                        type="button"
                        onClick={handleSubmit}
                        disabled={processing}
                        className={`px-4 py-2 rounded-lg text-sm font-medium text-white ${
                            processing
                                ? 'bg-gray-400 cursor-not-allowed'
                                : 'bg-blue-600 hover:bg-blue-700'
                        }`}
                    >
                        {processing ? 'Saving...' : 'Save Changes'}
                    </button>
                </div>
            }
        >
            <Head title="Settings - Admin" />

            {/* Flash Messages */}
            {flash?.success && (
                <div className="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    {flash.success}
                </div>
            )}
            {flash?.error && (
                <div className="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    {flash.error}
                </div>
            )}

            <div className="flex space-x-6">
                {/* Sidebar Navigation */}
                <div className="w-64 bg-white shadow rounded-lg p-4">
                    <nav className="space-y-2">
                        {tabs.map((tab) => (
                            <button
                                key={tab.id}
                                type="button"
                                onClick={() => setActiveTab(tab.id)}
                                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                                    activeTab === tab.id
                                        ? 'bg-blue-600 text-white'
                                        : 'text-gray-700 hover:bg-gray-100'
                                }`}
                            >
                                <tab.icon className="h-5 w-5 mr-3" />
                                {tab.name}
                            </button>
                        ))}
                    </nav>
                </div>

                {/* Main Content */}
                <div className="flex-1">
                    {renderContent()}
                </div>
            </div>
        </DashboardLayout>
    );
}
