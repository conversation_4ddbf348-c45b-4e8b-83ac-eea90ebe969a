import { Head } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { PageProps } from '@/types';
import { useEffect } from 'react';
import { gsap } from 'gsap';
import {
    UsersIcon,
    HomeIcon,
    CalendarIcon,
    CurrencyDollarIcon,
    StarIcon,
    ArrowUpIcon,
    ArrowDownIcon,
} from '@heroicons/react/24/outline';

interface DashboardStats {
    total_users: number;
    total_properties: number;
    active_properties: number;
    total_bookings: number;
    monthly_revenue: number;
    pending_reviews: number;
    total_payments: number;
}

interface User {
    id: number;
    name: string;
    email: string;
    user_type: string;
    created_at: string;
}

interface Property {
    id: number;
    title: string;
    price: number;
    city: string;
    created_at: string;
    user: User;
}

interface Booking {
    id: number;
    type: string;
    status: string;
    created_at: string;
    user: User;
    property: Property;
}

interface AdminDashboardProps extends PageProps {
    stats: DashboardStats;
    userTypes: Record<string, number>;
    propertyTypes: Record<string, number>;
    recentUsers: User[];
    recentProperties: Property[];
    recentBookings: Booking[];
}

export default function Dashboard({
    auth,
    stats,
    userTypes,
    propertyTypes,
    recentUsers,
    recentProperties,
    recentBookings,
}: AdminDashboardProps) {
    useEffect(() => {
        // Animate dashboard elements on load
        gsap.fromTo('.dashboard-card',
            { y: 30, opacity: 0 },
            { y: 0, opacity: 1, duration: 0.8, stagger: 0.1, ease: "power3.out" }
        );

        gsap.fromTo('.chart-container',
            { x: -30, opacity: 0 },
            { x: 0, opacity: 1, duration: 0.8, stagger: 0.2, delay: 0.3, ease: "power3.out" }
        );

        gsap.fromTo('.activity-item',
            { scale: 0.9, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.6, stagger: 0.05, delay: 0.6, ease: "back.out(1.7)" }
        );
    }, []);

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    };

    const statCards = [
        {
            title: 'Total Users',
            value: stats.total_users,
            icon: UsersIcon,
            color: 'bg-gradient-to-r from-blue-500 to-blue-600',
            change: '+12%',
            trend: 'up',
            subtitle: 'Active users'
        },
        {
            title: 'Total Properties',
            value: stats.total_properties,
            icon: HomeIcon,
            color: 'bg-gradient-to-r from-green-500 to-green-600',
            change: '+8%',
            trend: 'up',
            subtitle: `${stats.active_properties} active`
        },
        {
            title: 'Total Bookings',
            value: stats.total_bookings,
            icon: CalendarIcon,
            color: 'bg-gradient-to-r from-purple-500 to-purple-600',
            change: '+15%',
            trend: 'up',
            subtitle: 'This month'
        },
        {
            title: 'Monthly Revenue',
            value: formatCurrency(stats.monthly_revenue),
            icon: CurrencyDollarIcon,
            color: 'bg-gradient-to-r from-yellow-500 to-yellow-600',
            change: '+22%',
            trend: 'up',
            subtitle: 'vs last month'
        },
        {
            title: 'Pending Reviews',
            value: stats.pending_reviews,
            icon: StarIcon,
            color: 'bg-gradient-to-r from-orange-500 to-orange-600',
            change: '-5%',
            trend: 'down',
            subtitle: 'Awaiting approval'
        },
        {
            title: 'Total Payments',
            value: stats.total_payments,
            icon: CurrencyDollarIcon,
            color: 'bg-gradient-to-r from-pink-500 to-pink-600',
            change: '+18%',
            trend: 'up',
            subtitle: 'Processed'
        },
    ];

    return (
        <DashboardLayout
            user={auth.user}
            header={
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
                        <p className="text-gray-600 mt-1">Welcome back, {auth.user.name}</p>
                    </div>
                    <div className="flex items-center space-x-4">
                        <div className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                            Last updated: {new Date().toLocaleDateString()}
                        </div>
                    </div>
                </div>
            }
        >
            <Head title="Admin Dashboard - PropertyHub" />

            <div className="space-y-6">
                {/* Stats Grid */}
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
                    {statCards.map((stat, index) => (
                        <div key={index} className="dashboard-card bg-white overflow-hidden shadow-sm rounded-xl border border-gray-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                            <div className="p-6">
                                <div className="flex items-center justify-between">
                                    <div className="flex-1">
                                        <div className="flex items-center justify-between mb-4">
                                            <div className={`flex-shrink-0 p-3 rounded-xl ${stat.color} shadow-lg`}>
                                                <stat.icon className="h-6 w-6 text-white" />
                                            </div>
                                            <div className={`flex items-center text-sm font-medium ${
                                                stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                                            }`}>
                                                {stat.trend === 'up' ? (
                                                    <ArrowUpIcon className="h-4 w-4 mr-1" />
                                                ) : (
                                                    <ArrowDownIcon className="h-4 w-4 mr-1" />
                                                )}
                                                {stat.change}
                                            </div>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-gray-500 mb-1">
                                                {stat.title}
                                            </p>
                                            <p className="text-3xl font-bold text-gray-900 mb-1">
                                                {stat.value}
                                            </p>
                                            <p className="text-xs text-gray-500">
                                                {stat.subtitle}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Charts and Analytics */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* User Types Chart */}
                    <div className="chart-container bg-white overflow-hidden shadow-sm rounded-xl border border-gray-100 hover:shadow-lg transition-shadow duration-300">
                        <div className="p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                                <UsersIcon className="h-5 w-5 mr-2 text-blue-600" />
                                User Distribution
                            </h3>
                            <div className="space-y-4">
                                {Object.entries(userTypes).map(([type, count]) => {
                                    const total = Object.values(userTypes).reduce((a, b) => a + b, 0);
                                    const percentage = ((count / total) * 100).toFixed(1);
                                    return (
                                        <div key={type} className="activity-item">
                                            <div className="flex items-center justify-between mb-2">
                                                <span className="text-sm font-medium text-gray-700 capitalize">
                                                    {type}
                                                </span>
                                                <span className="text-sm font-bold text-gray-900">{count}</span>
                                            </div>
                                            <div className="w-full bg-gray-200 rounded-full h-2.5">
                                                <div
                                                    className="bg-gradient-to-r from-blue-500 to-blue-600 h-2.5 rounded-full transition-all duration-500"
                                                    style={{ width: `${percentage}%` }}
                                                ></div>
                                            </div>
                                            <div className="text-xs text-gray-500 mt-1">{percentage}%</div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    </div>

                    {/* Property Types Chart */}
                    <div className="chart-container bg-white overflow-hidden shadow-sm rounded-xl border border-gray-100 hover:shadow-lg transition-shadow duration-300">
                        <div className="p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                                <HomeIcon className="h-5 w-5 mr-2 text-green-600" />
                                Property Distribution
                            </h3>
                            <div className="space-y-4">
                                {Object.entries(propertyTypes).map(([type, count]) => {
                                    const total = Object.values(propertyTypes).reduce((a, b) => a + b, 0);
                                    const percentage = ((count / total) * 100).toFixed(1);
                                    return (
                                        <div key={type} className="activity-item">
                                            <div className="flex items-center justify-between mb-2">
                                                <span className="text-sm font-medium text-gray-700 capitalize">
                                                    {type}
                                                </span>
                                                <span className="text-sm font-bold text-gray-900">{count}</span>
                                            </div>
                                            <div className="w-full bg-gray-200 rounded-full h-2.5">
                                                <div
                                                    className="bg-gradient-to-r from-green-500 to-green-600 h-2.5 rounded-full transition-all duration-500"
                                                    style={{ width: `${percentage}%` }}
                                                ></div>
                                            </div>
                                            <div className="text-xs text-gray-500 mt-1">{percentage}%</div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Recent Activity */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Recent Users */}
                    <div className="bg-white overflow-hidden shadow-sm rounded-xl border border-gray-100 hover:shadow-lg transition-shadow duration-300">
                        <div className="p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                                <UsersIcon className="h-5 w-5 mr-2 text-blue-600" />
                                Recent Users
                            </h3>
                            <div className="space-y-4">
                                {recentUsers.map((user) => (
                                    <div key={user.id} className="activity-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                        <div className="flex-shrink-0">
                                            <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center shadow-md">
                                                <span className="text-sm font-medium text-white">
                                                    {user.name.charAt(0).toUpperCase()}
                                                </span>
                                            </div>
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <p className="text-sm font-medium text-gray-900 truncate">
                                                {user.name}
                                            </p>
                                            <p className="text-xs text-gray-500 capitalize">
                                                {user.user_type} • {formatDate(user.created_at)}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Recent Properties */}
                    <div className="bg-white overflow-hidden shadow-sm rounded-xl border border-gray-100 hover:shadow-lg transition-shadow duration-300">
                        <div className="p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                                <HomeIcon className="h-5 w-5 mr-2 text-green-600" />
                                Recent Properties
                            </h3>
                            <div className="space-y-4">
                                {recentProperties.map((property) => (
                                    <div key={property.id} className="activity-item border-l-4 border-blue-400 pl-4 py-3 hover:bg-gray-50 rounded-r-lg transition-colors">
                                        <p className="text-sm font-medium text-gray-900 truncate">
                                            {property.title}
                                        </p>
                                        <p className="text-xs text-gray-500 mt-1">
                                            {formatCurrency(property.price)} • {property.city}
                                        </p>
                                        <p className="text-xs text-gray-400 mt-1">
                                            by {property.user.name} • {formatDate(property.created_at)}
                                        </p>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Recent Bookings */}
                    <div className="bg-white overflow-hidden shadow-sm rounded-xl border border-gray-100 hover:shadow-lg transition-shadow duration-300">
                        <div className="p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                                <CalendarIcon className="h-5 w-5 mr-2 text-purple-600" />
                                Recent Bookings
                            </h3>
                            <div className="space-y-4">
                                {recentBookings.map((booking) => (
                                    <div key={booking.id} className="activity-item border-l-4 border-green-400 pl-4 py-3 hover:bg-gray-50 rounded-r-lg transition-colors">
                                        <p className="text-sm font-medium text-gray-900 truncate">
                                            {booking.property.title}
                                        </p>
                                        <p className="text-xs text-gray-500 capitalize mt-1">
                                            {booking.type} • {booking.status}
                                        </p>
                                        <p className="text-xs text-gray-400 mt-1">
                                            by {booking.user.name} • {formatDate(booking.created_at)}
                                        </p>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    );
}
