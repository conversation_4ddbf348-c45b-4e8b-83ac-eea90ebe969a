<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Payment;
use App\Models\Message;
use App\Models\Favorite;
use App\Models\Review;
use App\Models\Property;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class TenantDashboardController extends Controller
{


    /**
     * Display the tenant dashboard
     */
    public function index()
    {
        $userId = Auth::id();

        // Get dashboard statistics
        $stats = [
            'total_bookings' => Booking::where('user_id', $userId)->count(),
            'pending_bookings' => Booking::where('user_id', $userId)
                ->where('status', 'pending')->count(),
            'confirmed_bookings' => Booking::where('user_id', $userId)
                ->where('status', 'confirmed')->count(),
            'total_payments' => Payment::where('user_id', $userId)->count(),
            'total_spent' => Payment::where('user_id', $userId)
                ->where('status', 'paid')->sum('amount'),
            'pending_payments' => Payment::where('user_id', $userId)
                ->where('status', 'pending')->count(),
            'favorite_properties' => Favorite::where('user_id', $userId)->count(),
            'unread_messages' => Message::where('receiver_id', $userId)
                ->where('is_read', false)->count(),
            'reviews_given' => Review::where('user_id', $userId)->count(),
        ];

        // Favorite properties
        $favoriteProperties = Favorite::with(['property.primaryImage', 'property.user'])
            ->where('user_id', $userId)
            ->latest()
            ->take(6)
            ->get();

        // Recent bookings
        $recentBookings = Booking::with(['property.primaryImage', 'property.user'])
            ->where('user_id', $userId)
            ->latest()
            ->take(5)
            ->get();

        // Recent payments
        $recentPayments = Payment::with(['property'])
            ->where('user_id', $userId)
            ->latest()
            ->take(5)
            ->get();

        // Spending by month (last 6 months)
        $monthlySpending = Payment::select(
            DB::raw('YEAR(paid_at) as year'),
            DB::raw('MONTH(paid_at) as month'),
            DB::raw('SUM(amount) as total')
        )
        ->where('user_id', $userId)
        ->where('status', 'paid')
        ->where('paid_at', '>=', now()->subMonths(6))
        ->groupBy('year', 'month')
        ->orderBy('year', 'asc')
        ->orderBy('month', 'asc')
        ->get();

        // Property recommendations
        $recommendations = $this->getRecommendations($userId);

        return Inertia::render('Tenant/Dashboard', [
            'stats' => $stats,
            'favoriteProperties' => $favoriteProperties,
            'recentBookings' => $recentBookings,
            'recentPayments' => $recentPayments,
            'monthlySpending' => $monthlySpending,
            'recommendations' => $recommendations,
        ]);
    }

    /**
     * Display bookings page
     */
    public function bookings()
    {
        $userId = Auth::id();
        $bookings = Booking::where('user_id', $userId)
            ->with(['property.primaryImage', 'property.user'])
            ->latest()
            ->paginate(20);

        return Inertia::render('Tenant/Bookings', [
            'bookings' => $bookings,
        ]);
    }

    /**
     * Display favorites page
     */
    public function favorites()
    {
        $userId = Auth::id();
        $favorites = Favorite::where('user_id', $userId)
            ->with(['property.primaryImage', 'property.user'])
            ->latest()
            ->paginate(20);

        return Inertia::render('Tenant/Favorites', [
            'favorites' => $favorites,
        ]);
    }

    /**
     * Display messages page
     */
    public function messages()
    {
        $userId = Auth::id();
        $messages = Message::where('receiver_id', $userId)
            ->orWhere('sender_id', $userId)
            ->with(['sender', 'receiver', 'property'])
            ->latest()
            ->paginate(20);

        return Inertia::render('Tenant/Messages', [
            'messages' => $messages,
        ]);
    }

    /**
     * Display payments page
     */
    public function payments()
    {
        $userId = Auth::id();
        $payments = Payment::where('user_id', $userId)
            ->with(['property'])
            ->latest()
            ->paginate(20);

        return Inertia::render('Tenant/Payments', [
            'payments' => $payments,
        ]);
    }

    /**
     * Display reviews page
     */
    public function reviews()
    {
        $userId = Auth::id();
        $reviews = Review::where('user_id', $userId)
            ->with(['property'])
            ->latest()
            ->paginate(20);

        return Inertia::render('Tenant/Reviews', [
            'reviews' => $reviews,
        ]);
    }

    /**
     * Get property recommendations for tenant
     */
    private function getRecommendations($userId)
    {
        // Get user's favorite property types and price ranges
        $favoriteTypes = Favorite::join('properties', 'favorites.property_id', '=', 'properties.id')
            ->where('favorites.user_id', $userId)
            ->pluck('properties.type')
            ->unique()
            ->toArray();

        $averagePrice = Favorite::join('properties', 'favorites.property_id', '=', 'properties.id')
            ->where('favorites.user_id', $userId)
            ->avg('properties.price');

        // Get user's preferred locations
        $preferredCities = Favorite::join('properties', 'favorites.property_id', '=', 'properties.id')
            ->where('favorites.user_id', $userId)
            ->pluck('properties.city')
            ->unique()
            ->toArray();

        // Build recommendation query
        $query = Property::with(['primaryImage', 'user'])
            ->available()
            ->whereNotIn('id', function ($q) use ($userId) {
                $q->select('property_id')
                  ->from('favorites')
                  ->where('user_id', $userId);
            });

        // Apply filters based on preferences
        if (!empty($favoriteTypes)) {
            $query->whereIn('type', $favoriteTypes);
        }

        if ($averagePrice) {
            $priceRange = $averagePrice * 0.3; // 30% range
            $query->whereBetween('price', [
                $averagePrice - $priceRange,
                $averagePrice + $priceRange
            ]);
        }

        if (!empty($preferredCities)) {
            $query->whereIn('city', $preferredCities);
        }

        return $query->inRandomOrder()->limit(6)->get();
    }
}
