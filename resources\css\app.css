@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global Layout Improvements */
@layer components {
    /* Property Grid Consistency */
    .property-grid {
        @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
    }

    .property-grid-3 {
        @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
    }

    /* Container Consistency */
    .page-container {
        @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
    }

    .section-spacing {
        @apply py-8;
    }

    /* Card Consistency */
    .card {
        @apply bg-white rounded-lg shadow-md hover:shadow-xl transition-all duration-300;
    }

    .card-hover {
        @apply hover:-translate-y-1;
    }

/* Dashboard Improvements */
    .dashboard-card {
        @apply relative;
    }

    .dashboard-card::before {
        content: '';
        @apply absolute inset-0 bg-gradient-to-br from-white to-gray-50 rounded-xl opacity-0 transition-opacity duration-300;
    }

    .dashboard-card:hover::before {
        @apply opacity-100;
    }

    .dashboard-card > * {
        @apply relative z-10;
    }

    .chart-container {
        @apply relative;
    }

    .activity-item {
        @apply relative;
    }

    .activity-item:hover {
        @apply transform scale-[1.02] transition-transform duration-200;
    }

    /* Ensure consistent grid heights */
    .grid > .dashboard-card,
    .grid > .chart-container,
    .grid > div {
        @apply h-full;
    }

    /* Smooth animations */
    .dashboard-card,
    .chart-container,
    .activity-item {
        @apply transition-all duration-300 ease-in-out;
    }

    /* Better spacing for mobile */
    @media (max-width: 640px) {
        .dashboard-card {
            @apply mx-2;
        }
    }

    /* Dashboard content alignment */
    .dashboard-content {
        @apply flex-1 w-full;
    }

    /* Ensure proper container alignment */
    .dashboard-container {
        @apply w-full max-w-none;
    }

    /* Fix any potential layout shifts */
    .sidebar-item {
        @apply flex-shrink-0;
    }

    /* Dashboard layout fixes */
    .dashboard-layout {
        @apply flex min-h-screen;
    }

    .dashboard-sidebar {
        @apply w-64 flex-shrink-0;
    }

    .dashboard-main {
        @apply flex-1 flex flex-col min-w-0;
    }

    /* Mobile dashboard fixes */
    @media (max-width: 1024px) {
        .dashboard-sidebar {
            @apply fixed inset-y-0 left-0 z-50;
        }

        .dashboard-main {
            @apply w-full;
        }
    }

    /* Ensure proper spacing on all screen sizes */
    .dashboard-content {
        @apply w-full;
    }

    .dashboard-content > div {
        @apply w-full max-w-none;
    }

    /* Fix potential overflow issues */
    .dashboard-card {
        @apply w-full;
    }

    /* Sidebar footer positioning */
    .sidebar-footer {
        @apply mt-auto flex-shrink-0;
    }
}
