<?php

use App\Http\Controllers\Api\PropertyController;
use App\Http\Controllers\Api\BookingController;
use App\Http\Controllers\Api\MessageController;
use App\Http\Controllers\Api\FavoriteController;
use App\Http\Controllers\Api\ReviewController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\AdminDashboardController;
use App\Http\Controllers\Api\LandlordDashboardController;
use App\Http\Controllers\Api\TenantDashboardController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\PropertyComparisonController;
use App\Http\Controllers\Api\AvailabilityController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Public routes
Route::get('/properties', [PropertyController::class, 'index']);
Route::get('/properties/featured', [PropertyController::class, 'featured']);
Route::get('/properties/{id}', [PropertyController::class, 'show']);

// Public reviews for properties
Route::get('/reviews', [ReviewController::class, 'index']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Properties
    Route::post('/properties', [PropertyController::class, 'store']);
    Route::put('/properties/{id}', [PropertyController::class, 'update']);
    Route::delete('/properties/{id}', [PropertyController::class, 'destroy']);
    Route::get('/my-properties', [PropertyController::class, 'myProperties']);

    // Bookings
    Route::apiResource('bookings', BookingController::class);
    Route::get('/my-bookings', [BookingController::class, 'myBookings']);
    Route::patch('/bookings/{id}/status', [BookingController::class, 'updateStatus']);

    // Messages
    Route::apiResource('messages', MessageController::class);
    Route::patch('/messages/{id}/read', [MessageController::class, 'markAsRead']);
    Route::get('/messages/unread/count', [MessageController::class, 'unreadCount']);

    // Favorites
    Route::apiResource('favorites', FavoriteController::class);
    Route::post('/favorites/toggle', [FavoriteController::class, 'toggle']);

    // Reviews
    Route::apiResource('reviews', ReviewController::class)->except(['index']);
    Route::patch('/reviews/{id}/approve', [ReviewController::class, 'approve']);
    Route::get('/reviews/pending', [ReviewController::class, 'pending']);

    // Payments
    Route::apiResource('payments', PaymentController::class);
    Route::get('/my-payments', [PaymentController::class, 'myPayments']);
    Route::get('/payments/{id}/receipt', [PaymentController::class, 'generateReceipt']);

    // Notifications
    Route::get('/notifications', [NotificationController::class, 'index']);
    Route::get('/notifications/unread/count', [NotificationController::class, 'unreadCount']);
    Route::patch('/notifications/{id}/read', [NotificationController::class, 'markAsRead']);
    Route::patch('/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);
    Route::delete('/notifications/{id}', [NotificationController::class, 'destroy']);
    Route::delete('/notifications/read', [NotificationController::class, 'deleteRead']);

    // Property Comparison
    Route::post('/properties/compare', [PropertyComparisonController::class, 'compare']);
    Route::get('/properties/{id}/similar', [PropertyComparisonController::class, 'getSimilarProperties']);

    // Availability Calendar
    Route::get('/properties/{id}/availability', [AvailabilityController::class, 'getCalendar']);
    Route::post('/properties/{id}/availability/check', [AvailabilityController::class, 'checkAvailability']);
    Route::get('/properties/{id}/availability/ranges', [AvailabilityController::class, 'getAvailableRanges']);
    Route::put('/properties/{id}/availability', [AvailabilityController::class, 'updateAvailability']);
    Route::put('/properties/{id}/availability/bulk', [AvailabilityController::class, 'bulkUpdateAvailability']);

    // Admin Dashboard Routes
    Route::prefix('admin')->group(function () {
        Route::get('/stats', [AdminDashboardController::class, 'stats']);
        Route::get('/users', [AdminDashboardController::class, 'users']);
        Route::patch('/users/{id}/status', [AdminDashboardController::class, 'updateUserStatus']);
        Route::get('/properties', [AdminDashboardController::class, 'properties']);
        Route::patch('/properties/{id}/featured', [AdminDashboardController::class, 'updatePropertyFeatured']);
        Route::get('/reviews/pending', [AdminDashboardController::class, 'pendingReviews']);
        Route::patch('/reviews/{id}/status', [AdminDashboardController::class, 'updateReviewStatus']);
        Route::get('/activities', [AdminDashboardController::class, 'recentActivities']);
    });

    // Landlord Dashboard Routes
    Route::prefix('landlord')->group(function () {
        Route::get('/stats', [LandlordDashboardController::class, 'stats']);
        Route::get('/properties', [LandlordDashboardController::class, 'properties']);
        Route::get('/bookings', [LandlordDashboardController::class, 'bookings']);
        Route::get('/payments', [LandlordDashboardController::class, 'payments']);
        Route::get('/messages', [LandlordDashboardController::class, 'messages']);
        Route::get('/reviews', [LandlordDashboardController::class, 'reviews']);
        Route::get('/activities', [LandlordDashboardController::class, 'recentActivities']);
    });

    // Tenant Dashboard Routes
    Route::prefix('tenant')->group(function () {
        Route::get('/stats', [TenantDashboardController::class, 'stats']);
        Route::get('/bookings', [TenantDashboardController::class, 'bookings']);
        Route::get('/payments', [TenantDashboardController::class, 'payments']);
        Route::get('/favorites', [TenantDashboardController::class, 'favorites']);
        Route::get('/messages', [TenantDashboardController::class, 'messages']);
        Route::get('/reviews', [TenantDashboardController::class, 'reviews']);
        Route::get('/recommendations', [TenantDashboardController::class, 'recommendations']);
        Route::get('/activities', [TenantDashboardController::class, 'recentActivities']);
    });
});

// Xendit webhook (public route with token verification)
Route::post('/payments/xendit/callback', [PaymentController::class, 'xenditCallback']);
