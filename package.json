{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "tsc && vite build", "dev": "vite"}, "devDependencies": {"@headlessui/react": "^2.2.4", "@inertiajs/react": "^2.0.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/vite": "^4.0.0", "@types/node": "^18.13.0", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.12", "axios": "^1.9.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.2.1", "typescript": "^5.0.2", "vite": "^6.2.4"}, "dependencies": {"@heroicons/react": "^2.2.0", "@types/leaflet": "^1.9.18", "gsap": "^3.13.0", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1"}}