<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Review;
use App\Models\Property;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ReviewController extends Controller
{
    /**
     * Display a listing of reviews for a property
     */
    public function index(Request $request)
    {
        $propertyId = $request->query('property_id');

        if (!$propertyId) {
            return response()->json(['message' => 'Property ID is required'], 400);
        }

        $reviews = Review::with(['user'])
            ->where('property_id', $propertyId)
            ->where('is_approved', true)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json($reviews);
    }

    /**
     * Store a newly created review
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'property_id' => 'required|exists:properties,id',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $property = Property::findOrFail($request->property_id);

        // Check if user is not the property owner
        if ($property->user_id === Auth::id()) {
            return response()->json([
                'message' => 'You cannot review your own property'
            ], 400);
        }

        // Check if user has already reviewed this property
        $existingReview = Review::where('property_id', $request->property_id)
            ->where('user_id', Auth::id())
            ->first();

        if ($existingReview) {
            return response()->json([
                'message' => 'You have already reviewed this property'
            ], 400);
        }

        $review = Review::create([
            'property_id' => $request->property_id,
            'user_id' => Auth::id(),
            'rating' => $request->rating,
            'comment' => $request->comment,
            'is_approved' => false, // Reviews need approval
        ]);

        $review->load(['user']);

        return response()->json([
            'message' => 'Review submitted successfully and is pending approval',
            'review' => $review
        ], 201);
    }

    /**
     * Display the specified review
     */
    public function show(string $id)
    {
        $review = Review::with(['user', 'property'])->findOrFail($id);

        return response()->json($review);
    }

    /**
     * Update the specified review
     */
    public function update(Request $request, string $id)
    {
        $review = Review::findOrFail($id);

        // Check authorization - only review author can update
        if ($review->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'rating' => 'sometimes|integer|min:1|max:5',
            'comment' => 'sometimes|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $review->update($request->only(['rating', 'comment']));
        $review->update(['is_approved' => false]); // Reset approval status

        $review->load(['user']);

        return response()->json([
            'message' => 'Review updated successfully and is pending approval',
            'review' => $review
        ]);
    }

    /**
     * Approve a review (for property owners or admins)
     */
    public function approve(string $id)
    {
        $review = Review::findOrFail($id);
        $user = Auth::user();

        // Check authorization - property owner or admin can approve
        if ($review->property->user_id !== $user->id && !$user->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $review->update(['is_approved' => true]);

        return response()->json([
            'message' => 'Review approved successfully',
            'review' => $review
        ]);
    }

    /**
     * Get pending reviews (for property owners and admins)
     */
    public function pending()
    {
        $user = Auth::user();
        $query = Review::with(['user', 'property'])
            ->where('is_approved', false);

        if (!$user->hasRole('admin')) {
            // Show only reviews for properties owned by this user
            $query->whereHas('property', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            });
        }

        $reviews = $query->orderBy('created_at', 'desc')->paginate(10);

        return response()->json($reviews);
    }

    /**
     * Remove the specified review
     */
    public function destroy(string $id)
    {
        $review = Review::findOrFail($id);
        $user = Auth::user();

        // Check authorization - review author, property owner, or admin can delete
        if ($review->user_id !== $user->id &&
            $review->property->user_id !== $user->id &&
            !$user->hasRole('admin')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $review->delete();

        return response()->json(['message' => 'Review deleted successfully']);
    }
}
