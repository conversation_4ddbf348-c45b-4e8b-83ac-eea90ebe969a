import React, { useState, useEffect, useRef } from 'react';
import {
    PaperAirplaneIcon,
    UserIcon,
    PhoneIcon,
    VideoCameraIcon,
    PaperClipIcon,
    FaceSmileIcon,
    XMarkIcon,
    CheckIcon
} from '@heroicons/react/24/outline';
import { gsap } from 'gsap';

interface User {
    id: number;
    name: string;
    avatar?: string;
    user_type: string;
    is_online?: boolean;
    last_seen?: string;
}

interface Message {
    id: number;
    content: string;
    sender_id: number;
    receiver_id: number;
    created_at: string;
    read_at?: string;
    message_type: 'text' | 'image' | 'file';
    attachment_url?: string;
    attachment_name?: string;
}

interface Conversation {
    id: number;
    property_id?: number;
    property_title?: string;
    participants: User[];
    last_message?: Message;
    unread_count: number;
    updated_at: string;
}

interface MessagingSystemProps {
    currentUser: User;
    conversations: Conversation[];
    activeConversationId?: number;
    onSendMessage: (conversationId: number, content: string, type?: string) => void;
    onMarkAsRead: (conversationId: number) => void;
    onClose?: () => void;
    isModal?: boolean;
}

const MessagingSystem: React.FC<MessagingSystemProps> = ({
    currentUser,
    conversations,
    activeConversationId,
    onSendMessage,
    onMarkAsRead,
    onClose,
    isModal = false,
}) => {
    const [selectedConversation, setSelectedConversation] = useState<number | null>(activeConversationId || null);
    const [messageInput, setMessageInput] = useState('');
    const [messages, setMessages] = useState<Message[]>([]);
    const [isTyping, setIsTyping] = useState(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        // Animate messaging interface
        gsap.fromTo('.messaging-container',
            { scale: 0.95, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.4, ease: "power2.out" }
        );

        gsap.fromTo('.conversation-item',
            { x: -20, opacity: 0 },
            { x: 0, opacity: 1, duration: 0.3, stagger: 0.05, delay: 0.2, ease: "power2.out" }
        );
    }, []);

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    useEffect(() => {
        if (selectedConversation) {
            // Mock loading messages for selected conversation
            loadMessages(selectedConversation);
            onMarkAsRead(selectedConversation);
        }
    }, [selectedConversation]);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    const loadMessages = (conversationId: number) => {
        // Mock messages - in real app, this would be an API call
        const mockMessages: Message[] = [
            {
                id: 1,
                content: "Hi! I'm interested in your property listing.",
                sender_id: currentUser.id === 1 ? 2 : 1,
                receiver_id: currentUser.id,
                created_at: '2024-01-15T10:00:00Z',
                read_at: '2024-01-15T10:05:00Z',
                message_type: 'text',
            },
            {
                id: 2,
                content: "Hello! Thank you for your interest. I'd be happy to help you with any questions.",
                sender_id: currentUser.id,
                receiver_id: currentUser.id === 1 ? 2 : 1,
                created_at: '2024-01-15T10:05:00Z',
                read_at: '2024-01-15T10:10:00Z',
                message_type: 'text',
            },
            {
                id: 3,
                content: "Could we schedule a viewing for this weekend?",
                sender_id: currentUser.id === 1 ? 2 : 1,
                receiver_id: currentUser.id,
                created_at: '2024-01-15T10:10:00Z',
                message_type: 'text',
            },
        ];
        setMessages(mockMessages);
    };

    const handleSendMessage = () => {
        if (!messageInput.trim() || !selectedConversation) return;

        const newMessage: Message = {
            id: Date.now(),
            content: messageInput,
            sender_id: currentUser.id,
            receiver_id: selectedConversation,
            created_at: new Date().toISOString(),
            message_type: 'text',
        };

        setMessages(prev => [...prev, newMessage]);
        onSendMessage(selectedConversation, messageInput);
        setMessageInput('');

        // Animate new message
        setTimeout(() => {
            gsap.fromTo('.message-item:last-child',
                { scale: 0.8, opacity: 0 },
                { scale: 1, opacity: 1, duration: 0.3, ease: "back.out(1.7)" }
            );
        }, 50);
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    const formatTime = (dateString: string) => {
        return new Date(dateString).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const formatLastSeen = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

        if (diffInHours < 1) {
            return 'Active now';
        } else if (diffInHours < 24) {
            return `Active ${Math.floor(diffInHours)}h ago`;
        } else {
            return `Active ${Math.floor(diffInHours / 24)}d ago`;
        }
    };

    const getOtherParticipant = (conversation: Conversation): User => {
        return conversation.participants.find(p => p.id !== currentUser.id) || conversation.participants[0];
    };

    const selectedConversationData = conversations.find(c => c.id === selectedConversation);
    const otherUser = selectedConversationData ? getOtherParticipant(selectedConversationData) : null;

    return (
        <div className={`messaging-container bg-white rounded-lg shadow-xl overflow-hidden ${
            isModal ? 'max-w-4xl w-full max-h-[80vh]' : 'h-full'
        }`}>
            {/* Header */}
            {isModal && (
                <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-4">
                    <div className="flex items-center justify-between">
                        <h2 className="text-lg font-semibold">Messages</h2>
                        <button
                            onClick={onClose}
                            className="p-1 hover:bg-white/20 rounded-full transition-colors"
                        >
                            <XMarkIcon className="h-5 w-5" />
                        </button>
                    </div>
                </div>
            )}

            <div className="flex h-full">
                {/* Conversations List */}
                <div className="w-1/3 border-r border-gray-200 bg-gray-50">
                    <div className="p-4 border-b border-gray-200">
                        <h3 className="font-semibold text-gray-900">Conversations</h3>
                    </div>
                    <div className="overflow-y-auto h-full">
                        {conversations.map((conversation) => {
                            const otherParticipant = getOtherParticipant(conversation);
                            return (
                                <div
                                    key={conversation.id}
                                    className={`conversation-item p-4 border-b border-gray-100 cursor-pointer hover:bg-white transition-colors ${
                                        selectedConversation === conversation.id ? 'bg-white border-l-4 border-l-blue-600' : ''
                                    }`}
                                    onClick={() => setSelectedConversation(conversation.id)}
                                >
                                    <div className="flex items-center space-x-3">
                                        <div className="relative">
                                            <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                                {otherParticipant.avatar ? (
                                                    <img
                                                        src={otherParticipant.avatar}
                                                        alt={otherParticipant.name}
                                                        className="w-10 h-10 rounded-full object-cover"
                                                    />
                                                ) : (
                                                    <UserIcon className="h-6 w-6 text-gray-600" />
                                                )}
                                            </div>
                                            {otherParticipant.is_online && (
                                                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                                            )}
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <div className="flex items-center justify-between">
                                                <p className="text-sm font-medium text-gray-900 truncate">
                                                    {otherParticipant.name}
                                                </p>
                                                {conversation.unread_count > 0 && (
                                                    <span className="bg-blue-600 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                                                        {conversation.unread_count}
                                                    </span>
                                                )}
                                            </div>
                                            <p className="text-xs text-gray-500 capitalize">
                                                {otherParticipant.user_type}
                                            </p>
                                            {conversation.property_title && (
                                                <p className="text-xs text-blue-600 truncate">
                                                    Re: {conversation.property_title}
                                                </p>
                                            )}
                                            {conversation.last_message && (
                                                <p className="text-xs text-gray-500 truncate mt-1">
                                                    {conversation.last_message.content}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>

                {/* Chat Area */}
                <div className="flex-1 flex flex-col">
                    {selectedConversation && otherUser ? (
                        <>
                            {/* Chat Header */}
                            <div className="p-4 border-b border-gray-200 bg-white">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                            {otherUser.avatar ? (
                                                <img
                                                    src={otherUser.avatar}
                                                    alt={otherUser.name}
                                                    className="w-10 h-10 rounded-full object-cover"
                                                />
                                            ) : (
                                                <UserIcon className="h-6 w-6 text-gray-600" />
                                            )}
                                        </div>
                                        <div>
                                            <h4 className="font-medium text-gray-900">{otherUser.name}</h4>
                                            <p className="text-sm text-gray-500">
                                                {otherUser.is_online ? 'Online' : formatLastSeen(otherUser.last_seen || '')}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <button className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                                            <PhoneIcon className="h-5 w-5" />
                                        </button>
                                        <button className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                                            <VideoCameraIcon className="h-5 w-5" />
                                        </button>
                                    </div>
                                </div>
                            </div>

                            {/* Messages */}
                            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50">
                                {messages.map((message) => (
                                    <div
                                        key={message.id}
                                        className={`message-item flex ${
                                            message.sender_id === currentUser.id ? 'justify-end' : 'justify-start'
                                        }`}
                                    >
                                        <div
                                            className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                                                message.sender_id === currentUser.id
                                                    ? 'bg-blue-600 text-white'
                                                    : 'bg-white text-gray-900 border border-gray-200'
                                            }`}
                                        >
                                            <p className="text-sm">{message.content}</p>
                                            <div className={`flex items-center justify-between mt-1 ${
                                                message.sender_id === currentUser.id ? 'text-blue-100' : 'text-gray-500'
                                            }`}>
                                                <span className="text-xs">{formatTime(message.created_at)}</span>
                                                {message.sender_id === currentUser.id && (
                                                    <div className="ml-2">
                                                        {message.read_at ? (
                                                            <div className="flex">
                                                                <CheckIcon className="h-3 w-3" />
                                                                <CheckIcon className="h-3 w-3 -ml-1" />
                                                            </div>
                                                        ) : (
                                                            <CheckIcon className="h-3 w-3" />
                                                        )}
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                                {isTyping && (
                                    <div className="flex justify-start">
                                        <div className="bg-white text-gray-900 border border-gray-200 px-4 py-2 rounded-lg">
                                            <div className="flex space-x-1">
                                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                            </div>
                                        </div>
                                    </div>
                                )}
                                <div ref={messagesEndRef} />
                            </div>

                            {/* Message Input */}
                            <div className="p-4 border-t border-gray-200 bg-white">
                                <div className="flex items-center space-x-2">
                                    <button className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                                        <PaperClipIcon className="h-5 w-5" />
                                    </button>
                                    <div className="flex-1 relative">
                                        <input
                                            ref={inputRef}
                                            type="text"
                                            value={messageInput}
                                            onChange={(e) => setMessageInput(e.target.value)}
                                            onKeyPress={handleKeyPress}
                                            placeholder="Type a message..."
                                            className="w-full px-4 py-2 border border-gray-300 rounded-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                    <button className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                                        <FaceSmileIcon className="h-5 w-5" />
                                    </button>
                                    <button
                                        onClick={handleSendMessage}
                                        disabled={!messageInput.trim()}
                                        className="p-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        <PaperAirplaneIcon className="h-5 w-5" />
                                    </button>
                                </div>
                            </div>
                        </>
                    ) : (
                        <div className="flex-1 flex items-center justify-center bg-gray-50">
                            <div className="text-center">
                                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <UserIcon className="h-8 w-8 text-gray-400" />
                                </div>
                                <p className="text-gray-500">Select a conversation to start messaging</p>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default MessagingSystem;
