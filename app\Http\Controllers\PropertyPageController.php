<?php

namespace App\Http\Controllers;

use App\Models\Property;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class PropertyPageController extends Controller
{
    public function index(Request $request)
    {
        $query = Property::with(['user', 'primaryImage', 'images'])
            ->available();

        // Apply filters if provided
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('address', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }

        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }

        if ($request->has('listing_type') && $request->listing_type) {
            $query->where('listing_type', $request->listing_type);
        }

        if ($request->has('min_price') && $request->min_price) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->has('max_price') && $request->max_price) {
            $query->where('price', '<=', $request->max_price);
        }

        if ($request->has('bedrooms') && $request->bedrooms) {
            $query->where('bedrooms', '>=', $request->bedrooms);
        }

        if ($request->has('bathrooms') && $request->bathrooms) {
            $query->where('bathrooms', '>=', $request->bathrooms);
        }

        if ($request->has('city') && $request->city) {
            $query->where('city', 'like', "%{$request->city}%");
        }

        if ($request->has('featured') && $request->featured) {
            $query->featured();
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $properties = $query->paginate($request->get('per_page', 12));

        return Inertia::render('Properties/Index', [
            'properties' => $properties,
            'filters' => $request->only([
                'search', 'type', 'listing_type', 'min_price', 'max_price',
                'bedrooms', 'bathrooms', 'city', 'featured', 'sort_by', 'sort_order'
            ]),
        ]);
    }

    public function show($id)
    {
        $property = Property::with(['user', 'images', 'reviews.user'])
            ->findOrFail($id);

        // Increment views
        $property->incrementViews();

        // Add computed attributes
        $property->append(['formatted_price', 'average_rating']);

        // Check if user has favorited this property (if authenticated)
        if (Auth::check()) {
            $property->is_favorited = $property->favorites()
                ->where('user_id', Auth::id())
                ->exists();
        }

        // Add image URLs
        $property->images->each(function ($image) {
            // Check if it's an external URL or local path
            if (filter_var($image->image_path, FILTER_VALIDATE_URL)) {
                $image->image_url = $image->image_path;
            } else {
                $image->image_url = asset('storage/' . $image->image_path);
            }
        });

        return Inertia::render('Properties/Show', [
            'property' => $property,
        ]);
    }
}
