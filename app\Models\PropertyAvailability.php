<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class PropertyAvailability extends Model
{
    use HasFactory;

    protected $fillable = [
        'property_id',
        'date',
        'status',
        'price_override',
        'notes',
    ];

    protected function casts(): array
    {
        return [
            'date' => 'date',
            'price_override' => 'decimal:2',
        ];
    }

    // Relationships
    public function property()
    {
        return $this->belongsTo(Property::class);
    }

    // Scopes
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available');
    }

    public function scopeBooked($query)
    {
        return $query->where('status', 'booked');
    }

    public function scopeBlocked($query)
    {
        return $query->where('status', 'blocked');
    }

    public function scopeForDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    public function scopeForMonth($query, $year, $month)
    {
        return $query->whereYear('date', $year)
                    ->whereMonth('date', $month);
    }

    // Helper methods
    public function isAvailable()
    {
        return $this->status === 'available';
    }

    public function isBooked()
    {
        return $this->status === 'booked';
    }

    public function isBlocked()
    {
        return $this->status === 'blocked';
    }

    public function getEffectivePrice()
    {
        return $this->price_override ?? $this->property->price;
    }

    public static function generateCalendar($propertyId, $startDate, $endDate)
    {
        $property = Property::findOrFail($propertyId);
        $calendar = [];

        $current = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);

        while ($current <= $end) {
            $availability = static::where('property_id', $propertyId)
                ->where('date', $current->format('Y-m-d'))
                ->first();

            $calendar[] = [
                'date' => $current->format('Y-m-d'),
                'day_name' => $current->format('l'),
                'status' => $availability ? $availability->status : 'available',
                'price' => $availability ? $availability->getEffectivePrice() : $property->price,
                'notes' => $availability ? $availability->notes : null,
                'is_past' => $current->isPast(),
                'is_today' => $current->isToday(),
                'is_weekend' => $current->isWeekend(),
            ];

            $current->addDay();
        }

        return $calendar;
    }
}
