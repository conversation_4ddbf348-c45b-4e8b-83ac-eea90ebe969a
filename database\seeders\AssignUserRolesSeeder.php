<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class AssignUserRolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles if they don't exist
        $roles = ['admin', 'landlord', 'agent', 'tenant'];
        foreach ($roles as $roleName) {
            Role::firstOrCreate(['name' => $roleName]);
        }

        // Get all users without roles
        $users = User::doesntHave('roles')->get();

        foreach ($users as $user) {
            // Assign roles based on user_type or default to tenant
            switch ($user->user_type) {
                case 'admin':
                    $user->assignRole('admin');
                    break;
                case 'landlord':
                    $user->assignRole('landlord');
                    break;
                case 'agent':
                    $user->assignRole('agent');
                    break;
                case 'tenant':
                default:
                    $user->assignRole('tenant');
                    break;
            }
        }

        // Create a default admin user if none exists
        $adminUser = User::role('admin')->first();
        if (!$adminUser) {
            $admin = User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'user_type' => 'admin',
                'email_verified_at' => now(),
                'is_active' => true,
                'is_verified' => true,
            ]);
            $admin->assignRole('admin');
        }

        $this->command->info('User roles assigned successfully!');
    }
}
