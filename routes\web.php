<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\PropertyPageController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\AdminDashboardController;
use App\Http\Controllers\LandlordDashboardController;
use App\Http\Controllers\TenantDashboardController;
use App\Models\Property;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    $featuredProperties = Property::with(['user', 'primaryImage'])
        ->available()
        ->featured()
        ->limit(6)
        ->get();

    return Inertia::render('Welcome', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
        'laravelVersion' => Application::VERSION,
        'phpVersion' => PHP_VERSION,
        'featuredProperties' => $featuredProperties,
    ]);
});

Route::get('/properties', [PropertyPageController::class, 'index'])->name('properties.index');
Route::get('/properties/{id}', [PropertyPageController::class, 'show'])->name('properties.show');

// Main dashboard route - redirects to appropriate dashboard based on user role
Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// Role-specific dashboard routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Admin Dashboard Routes
    Route::middleware('role:admin')->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
        Route::get('/users', [AdminDashboardController::class, 'users'])->name('users');
        Route::get('/properties', [AdminDashboardController::class, 'properties'])->name('properties');
        Route::get('/bookings', [AdminDashboardController::class, 'bookings'])->name('bookings');
        Route::get('/payments', [AdminDashboardController::class, 'payments'])->name('payments');
        Route::get('/reviews', [AdminDashboardController::class, 'reviews'])->name('reviews');
        Route::get('/reports', [AdminDashboardController::class, 'reports'])->name('reports');
        Route::get('/settings', [AdminDashboardController::class, 'settings'])->name('settings');
        Route::patch('/settings', [AdminDashboardController::class, 'updateSettings'])->name('settings.update');
    });

    // Landlord Dashboard Routes
    Route::middleware('role:landlord|agent')->prefix('landlord')->name('landlord.')->group(function () {
        Route::get('/dashboard', [LandlordDashboardController::class, 'index'])->name('dashboard');
        Route::get('/properties', [LandlordDashboardController::class, 'properties'])->name('properties');
        Route::get('/bookings', [LandlordDashboardController::class, 'bookings'])->name('bookings');
        Route::get('/payments', [LandlordDashboardController::class, 'payments'])->name('payments');
        Route::get('/messages', [LandlordDashboardController::class, 'messages'])->name('messages');
        Route::get('/reviews', [LandlordDashboardController::class, 'reviews'])->name('reviews');
        Route::get('/analytics', [LandlordDashboardController::class, 'analytics'])->name('analytics');
    });

    // Tenant Dashboard Routes
    Route::middleware('role:tenant')->prefix('tenant')->name('tenant.')->group(function () {
        Route::get('/dashboard', [TenantDashboardController::class, 'index'])->name('dashboard');
        Route::get('/bookings', [TenantDashboardController::class, 'bookings'])->name('bookings');
        Route::get('/favorites', [TenantDashboardController::class, 'favorites'])->name('favorites');
        Route::get('/messages', [TenantDashboardController::class, 'messages'])->name('messages');
        Route::get('/payments', [TenantDashboardController::class, 'payments'])->name('payments');
        Route::get('/reviews', [TenantDashboardController::class, 'reviews'])->name('reviews');
    });
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
