import React from 'react';
import { Head } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { PageProps } from '@/types';
import { StarIcon } from '@heroicons/react/24/outline';

interface ReviewsPageProps extends PageProps {
    reviews: {
        data: any[];
        meta: any;
    };
}

export default function Reviews({ auth, reviews }: ReviewsPageProps) {
    return (
        <DashboardLayout
            user={auth.user}
            header={
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Reviews Management</h1>
                    <p className="text-gray-600">Manage all platform reviews</p>
                </div>
            }
        >
            <Head title="Reviews Management - Admin" />

            <div className="space-y-6">
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center">
                        <StarIcon className="h-8 w-8 text-yellow-600 mr-3" />
                        <div>
                            <h2 className="text-lg font-semibold text-gray-900">Reviews Management</h2>
                            <p className="text-gray-600">This page is under development</p>
                        </div>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    );
}
