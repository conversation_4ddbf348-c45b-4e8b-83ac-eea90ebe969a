<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Property;
use App\Models\Booking;
use App\Models\Payment;
use App\Models\Review;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class AdminDashboardController extends Controller
{


    /**
     * Display the admin dashboard
     */
    public function index()
    {
        // Get dashboard statistics
        $stats = [
            'total_users' => User::count(),
            'total_properties' => Property::count(),
            'total_bookings' => Booking::count(),
            'total_payments' => Payment::count(),
            'pending_reviews' => Review::where('is_approved', false)->count(),
            'active_properties' => Property::where('status', 'published')->count(),
            'total_revenue' => Payment::where('status', 'paid')->sum('amount'),
            'monthly_revenue' => Payment::where('status', 'paid')
                ->whereMonth('paid_at', now()->month)
                ->whereYear('paid_at', now()->year)
                ->sum('amount'),
        ];

        // User type breakdown
        $userTypes = User::select('user_type', DB::raw('count(*) as count'))
            ->groupBy('user_type')
            ->pluck('count', 'user_type');

        // Property type breakdown
        $propertyTypes = Property::select('type', DB::raw('count(*) as count'))
            ->groupBy('type')
            ->pluck('count', 'type');

        // Monthly revenue chart data (last 6 months)
        $monthlyRevenue = Payment::select(
            DB::raw('YEAR(paid_at) as year'),
            DB::raw('MONTH(paid_at) as month'),
            DB::raw('SUM(amount) as total')
        )
        ->where('status', 'paid')
        ->where('paid_at', '>=', now()->subMonths(6))
        ->groupBy('year', 'month')
        ->orderBy('year', 'asc')
        ->orderBy('month', 'asc')
        ->get();

        // Recent activities
        $recentUsers = User::latest()->take(5)->get(['id', 'name', 'email', 'user_type', 'created_at']);
        $recentProperties = Property::with('user')->latest()->take(5)->get();
        $recentBookings = Booking::with(['user', 'property'])->latest()->take(5)->get();

        return Inertia::render('Admin/Dashboard', [
            'stats' => $stats,
            'userTypes' => $userTypes,
            'propertyTypes' => $propertyTypes,
            'monthlyRevenue' => $monthlyRevenue,
            'recentUsers' => $recentUsers,
            'recentProperties' => $recentProperties,
            'recentBookings' => $recentBookings,
        ]);
    }

    /**
     * Display users management page
     */
    public function users()
    {
        $users = User::with('roles')->latest()->paginate(20);

        return Inertia::render('Admin/Users', [
            'users' => $users,
        ]);
    }

    /**
     * Display properties management page
     */
    public function properties()
    {
        try {
            $properties = Property::with(['user', 'primaryImage'])
                ->withCount(['bookings', 'favorites', 'reviews'])
                ->withAvg('reviews', 'rating')
                ->latest()
                ->paginate(20);

            return Inertia::render('Admin/Properties', [
                'properties' => $properties,
            ]);
        } catch (\Exception $e) {
            // Create simple empty pagination for fallback
            $emptyPagination = new \Illuminate\Pagination\LengthAwarePaginator(
                [],
                0,
                20,
                1,
                ['path' => request()->url()]
            );

            return Inertia::render('Admin/Properties', [
                'properties' => $emptyPagination,
            ]);
        }
    }

    /**
     * Display bookings management page
     */
    public function bookings()
    {
        try {
            $bookings = Booking::with(['user', 'property'])
                ->latest()
                ->paginate(20);

            return Inertia::render('Admin/Bookings', [
                'bookings' => $bookings,
            ]);
        } catch (\Exception $e) {
            $emptyPagination = new \Illuminate\Pagination\LengthAwarePaginator(
                [],
                0,
                20,
                1,
                ['path' => request()->url()]
            );

            return Inertia::render('Admin/Bookings', [
                'bookings' => $emptyPagination,
            ]);
        }
    }

    /**
     * Display payments management page
     */
    public function payments()
    {
        $payments = Payment::with(['user', 'property'])
            ->latest()
            ->paginate(20);

        return Inertia::render('Admin/Payments', [
            'payments' => $payments,
        ]);
    }

    /**
     * Display reviews management page
     */
    public function reviews()
    {
        $reviews = Review::with(['user', 'property'])
            ->latest()
            ->paginate(20);

        return Inertia::render('Admin/Reviews', [
            'reviews' => $reviews,
        ]);
    }

    /**
     * Display reports page
     */
    public function reports()
    {
        return Inertia::render('Admin/Reports');
    }

    /**
     * Display settings page
     */
    public function settings()
    {
        return Inertia::render('Admin/Settings');
    }

    /**
     * Update platform settings
     */
    public function updateSettings(Request $request)
    {
        // Log the incoming request for debugging
        \Log::info('Settings update request received', [
            'data' => $request->all(),
            'user' => $request->user()->id ?? 'unknown'
        ]);

        $validated = $request->validate([
            'platform_name' => 'required|string|max:255',
            'platform_url' => 'required|url|max:255',
            'contact_email' => 'required|email|max:255',
            'support_phone' => 'required|string|max:20',
            'default_currency' => 'required|string|max:3',
            'default_language' => 'required|string|max:5',
            'timezone' => 'required|string|max:50',
        ]);

        // Log successful validation
        \Log::info('Settings validation passed', $validated);

        // Here you would typically save to a settings table or config file
        // For now, we'll just return a success response
        // You might want to create a Settings model or use Laravel's config system

        \Log::info('Settings updated successfully');
        return redirect()->back()->with('success', 'Settings updated successfully!');
    }
}
