import { useEffect } from 'react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { Head, Link } from '@inertiajs/react';
import { PageProps } from '@/types';
import { gsap } from 'gsap';
import {
    CalendarIcon,
    CurrencyDollarIcon,
    HeartIcon,
    ChatBubbleLeftIcon,
    StarIcon,
    ClockIcon,
    HomeIcon,
    MagnifyingGlassIcon,
    ArrowUpIcon,
    ArrowDownIcon,
} from '@heroicons/react/24/outline';

interface Property {
    id: number;
    title: string;
    type: string;
    price: number;
    city: string;
    listing_type: string;
    primary_image?: {
        image_path: string;
    };
    user: {
        name: string;
    };
}

interface Favorite {
    id: number;
    property: Property;
}

interface DashboardProps extends PageProps {
    stats: {
        total_bookings: number;
        pending_bookings: number;
        confirmed_bookings: number;
        total_payments: number;
        total_spent: number;
        pending_payments: number;
        favorite_properties: number;
        unread_messages: number;
        reviews_given: number;
    };
    favoriteProperties: Favorite[];
    recentBookings: Array<{
        id: number;
        type: string;
        status: string;
        property: Property;
        created_at: string;
    }>;
    recentPayments: Array<{
        id: number;
        amount: number;
        status: string;
        payment_type: string;
        property: {
            title: string;
        };
        created_at: string;
    }>;
    monthlySpending: Array<{
        year: number;
        month: number;
        total: number;
    }>;
    recommendations: Property[];
}

export default function TenantDashboard({
    auth,
    stats,
    favoriteProperties,
    recentBookings,
    recentPayments,
    monthlySpending,
    recommendations
}: DashboardProps) {
    useEffect(() => {
        // Animate dashboard elements
        gsap.fromTo('.dashboard-card',
            { y: 30, opacity: 0 },
            { y: 0, opacity: 1, duration: 0.6, stagger: 0.1, ease: "power2.out" }
        );

        gsap.fromTo('.activity-item',
            { x: -20, opacity: 0 },
            { x: 0, opacity: 1, duration: 0.4, stagger: 0.05, delay: 0.3, ease: "power2.out" }
        );

        gsap.fromTo('.property-card',
            { scale: 0.95, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.5, stagger: 0.1, delay: 0.4, ease: "power2.out" }
        );
    }, []);
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP',
            minimumFractionDigits: 0,
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'confirmed': return 'bg-green-100 text-green-800';
            case 'paid': return 'bg-green-100 text-green-800';
            case 'cancelled': return 'bg-red-100 text-red-800';
            case 'failed': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const statCards = [
        {
            title: 'Total Bookings',
            value: stats.total_bookings,
            icon: CalendarIcon,
            color: 'bg-gradient-to-r from-blue-500 to-blue-600',
            change: '+12%',
            trend: 'up',
            subtitle: 'All time'
        },
        {
            title: 'Pending Bookings',
            value: stats.pending_bookings,
            icon: ClockIcon,
            color: 'bg-gradient-to-r from-yellow-500 to-yellow-600',
            change: '+5%',
            trend: 'up',
            subtitle: 'Awaiting approval'
        },
        {
            title: 'Favorite Properties',
            value: stats.favorite_properties,
            icon: HeartIcon,
            color: 'bg-gradient-to-r from-pink-500 to-pink-600',
            change: '+8%',
            trend: 'up',
            subtitle: 'Saved properties'
        },
        {
            title: 'Total Spent',
            value: formatCurrency(stats.total_spent),
            icon: CurrencyDollarIcon,
            color: 'bg-gradient-to-r from-emerald-500 to-emerald-600',
            change: '+15%',
            trend: 'up',
            subtitle: 'All payments'
        },
        {
            title: 'Unread Messages',
            value: stats.unread_messages,
            icon: ChatBubbleLeftIcon,
            color: 'bg-gradient-to-r from-purple-500 to-purple-600',
            change: '-2%',
            trend: 'down',
            subtitle: 'New messages'
        },
        {
            title: 'Reviews Given',
            value: stats.reviews_given,
            icon: StarIcon,
            color: 'bg-gradient-to-r from-indigo-500 to-indigo-600',
            change: '+3%',
            trend: 'up',
            subtitle: 'Property reviews'
        },
    ];

    return (
        <DashboardLayout
            user={auth.user}
            header={
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Tenant Dashboard</h1>
                        <p className="text-gray-600">Welcome back, {auth.user.name}</p>
                    </div>
                    <Link
                        href="/properties"
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center hover:scale-105 active:scale-95 transition-all duration-200 shadow-md hover:shadow-lg"
                    >
                        <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                        Browse Properties
                    </Link>
                </div>
            }
        >
            <Head title="Tenant Dashboard - PropertyHub" />

            <div className="space-y-6">
                {/* Stats Grid */}
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
                {statCards.map((stat, index) => (
                    <div key={index} className="dashboard-card bg-white overflow-hidden shadow-lg rounded-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                        <div className="p-6">
                            <div className="flex items-center">
                                <div className={`flex-shrink-0 p-3 rounded-lg ${stat.color} shadow-md`}>
                                    <stat.icon className="h-6 w-6 text-white" />
                                </div>
                                <div className="ml-4 flex-1">
                                    <p className="text-sm font-medium text-gray-500 truncate">
                                        {stat.title}
                                    </p>
                                    <div className="flex items-center">
                                        <p className="text-2xl font-bold text-gray-900">
                                            {stat.value}
                                        </p>
                                        <div className={`ml-2 flex items-center text-sm ${
                                            stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                                        }`}>
                                            {stat.trend === 'up' ? (
                                                <ArrowUpIcon className="h-4 w-4 mr-1" />
                                            ) : (
                                                <ArrowDownIcon className="h-4 w-4 mr-1" />
                                            )}
                                            {stat.change}
                                        </div>
                                    </div>
                                    <p className="text-xs text-gray-500 mt-1">
                                        {stat.subtitle}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
                </div>

                {/* Favorite Properties */}
                <div>
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6">
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-lg font-medium text-gray-900">Your Favorite Properties</h3>
                                <Link
                                    href="/favorites"
                                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                >
                                    View All
                                </Link>
                            </div>
                            <div className="property-grid-3">
                                {favoriteProperties.map((favorite) => (
                                    <div key={favorite.id} className="border rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                                        <div className="aspect-w-16 aspect-h-9 bg-gray-200">
                                            {favorite.property.primary_image ? (
                                                <img
                                                    src={`/storage/${favorite.property.primary_image.image_path}`}
                                                    alt={favorite.property.title}
                                                    className="w-full h-48 object-cover"
                                                />
                                            ) : (
                                                <div className="w-full h-48 bg-gray-300 flex items-center justify-center">
                                                    <HomeIcon className="h-12 w-12 text-gray-400" />
                                                </div>
                                            )}
                                        </div>
                                        <div className="p-4">
                                            <h4 className="font-medium text-gray-900 truncate">{favorite.property.title}</h4>
                                            <p className="text-sm text-gray-500 capitalize">
                                                {favorite.property.type} • {favorite.property.city}
                                            </p>
                                            <p className="text-lg font-semibold text-gray-900 mt-1">
                                                {formatCurrency(favorite.property.price)}
                                                {favorite.property.listing_type === 'rent' && <span className="text-sm font-normal">/month</span>}
                                            </p>
                                            <p className="text-xs text-gray-500 mt-2">
                                                by {favorite.property.user.name}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Recommendations */}
                {recommendations.length > 0 && (
                    <div>
                        <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div className="p-6">
                                <h3 className="text-lg font-medium text-gray-900 mb-4">Recommended for You</h3>
                                <div className="property-grid-3">
                                    {recommendations.map((property) => (
                                        <div key={property.id} className="border rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                                            <div className="aspect-w-16 aspect-h-9 bg-gray-200">
                                                {property.primary_image ? (
                                                    <img
                                                        src={`/storage/${property.primary_image.image_path}`}
                                                        alt={property.title}
                                                        className="w-full h-48 object-cover"
                                                    />
                                                ) : (
                                                    <div className="w-full h-48 bg-gray-300 flex items-center justify-center">
                                                        <HomeIcon className="h-12 w-12 text-gray-400" />
                                                    </div>
                                                )}
                                            </div>
                                            <div className="p-4">
                                                <h4 className="font-medium text-gray-900 truncate">{property.title}</h4>
                                                <p className="text-sm text-gray-500 capitalize">
                                                    {property.type} • {property.city}
                                                </p>
                                                <p className="text-lg font-semibold text-gray-900 mt-1">
                                                    {formatCurrency(property.price)}
                                                    {property.listing_type === 'rent' && <span className="text-sm font-normal">/month</span>}
                                                </p>
                                                <p className="text-xs text-gray-500 mt-2">
                                                    by {property.user.name}
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Recent Activity */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Recent Bookings */}
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Bookings</h3>
                            <div className="space-y-3">
                                {recentBookings.map((booking) => (
                                    <div key={booking.id} className="border-l-4 border-blue-400 pl-3">
                                        <p className="text-sm font-medium text-gray-900 truncate">
                                            {booking.property.title}
                                        </p>
                                        <p className="text-xs text-gray-500 capitalize">
                                            {booking.type} • {formatDate(booking.created_at)}
                                        </p>
                                        <span className={`inline-block px-2 py-1 text-xs rounded-full ${getStatusColor(booking.status)}`}>
                                            {booking.status}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Recent Payments */}
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Payments</h3>
                            <div className="space-y-3">
                                {recentPayments.map((payment) => (
                                    <div key={payment.id} className="border-l-4 border-green-400 pl-3">
                                        <p className="text-sm font-medium text-gray-900">
                                            {formatCurrency(payment.amount)}
                                        </p>
                                        <p className="text-xs text-gray-500">
                                            {payment.payment_type} • {payment.property.title}
                                        </p>
                                        <span className={`inline-block px-2 py-1 text-xs rounded-full ${getStatusColor(payment.status)}`}>
                                            {payment.status}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    );
}
