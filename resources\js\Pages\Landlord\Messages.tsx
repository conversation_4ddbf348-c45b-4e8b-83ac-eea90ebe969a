import React from 'react';
import { Head } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { PageProps } from '@/types';
import {
    ChatBubbleLeftRightIcon,
    UserIcon,
    HomeIcon,
    ClockIcon,
    MagnifyingGlassIcon,
    PaperAirplaneIcon,
    EllipsisVerticalIcon,
} from '@heroicons/react/24/outline';

interface Message {
    id: number;
    message: string;
    is_read: boolean;
    created_at: string;
    sender: {
        id: number;
        name: string;
        email: string;
    };
    receiver: {
        id: number;
        name: string;
        email: string;
    };
    property: {
        id: number;
        title: string;
        city: string;
    };
}

interface MessagesPageProps extends PageProps {
    messages: {
        data: Message[];
        links: any[];
        meta: any;
    };
}

export default function Messages({ auth, messages }: MessagesPageProps) {
    // Fallback for undefined messages
    const safeMessages = messages || { data: [], meta: { total: 0 }, links: [] };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

        if (diffInHours < 24) {
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        } else if (diffInHours < 168) { // 7 days
            return date.toLocaleDateString([], { weekday: 'short' });
        } else {
            return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
        }
    };

    const getMessageStats = () => {
        const total = safeMessages.data.length;
        const unread = safeMessages.data.filter(m => !m.is_read && m.receiver.id === auth.user.id).length;
        const conversations = new Set(safeMessages.data.map(m =>
            m.sender.id === auth.user.id ? m.receiver.id : m.sender.id
        )).size;

        return { total, unread, conversations };
    };

    const stats = getMessageStats();

    // Group messages by conversation
    const groupedMessages = safeMessages.data.reduce((acc, message) => {
        const otherUserId = message.sender.id === auth.user.id ? message.receiver.id : message.sender.id;
        const otherUser = message.sender.id === auth.user.id ? message.receiver : message.sender;

        if (!acc[otherUserId]) {
            acc[otherUserId] = {
                user: otherUser,
                property: message.property,
                messages: [],
                lastMessage: message,
                unreadCount: 0
            };
        }

        acc[otherUserId].messages.push(message);

        // Update last message if this one is newer
        if (new Date(message.created_at) > new Date(acc[otherUserId].lastMessage.created_at)) {
            acc[otherUserId].lastMessage = message;
        }

        // Count unread messages
        if (!message.is_read && message.receiver.id === auth.user.id) {
            acc[otherUserId].unreadCount++;
        }

        return acc;
    }, {} as Record<number, any>);

    const conversations = Object.values(groupedMessages).sort((a: any, b: any) =>
        new Date(b.lastMessage.created_at).getTime() - new Date(a.lastMessage.created_at).getTime()
    );

    return (
        <DashboardLayout
            user={auth.user}
            header={
                <div className="flex items-center justify-between w-full">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Messages</h1>
                        <p className="text-gray-600">Communicate with tenants and prospects</p>
                    </div>
                    <div className="flex items-center space-x-4">
                        <div className="relative">
                            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <input
                                type="text"
                                placeholder="Search conversations..."
                                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                        </div>
                    </div>
                </div>
            }
        >
            <Head title="Messages - Landlord" />

            <div className="space-y-6">
                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-purple-100">
                                <ChatBubbleLeftRightIcon className="h-6 w-6 text-purple-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Total Messages</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-red-100">
                                <ChatBubbleLeftRightIcon className="h-6 w-6 text-red-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Unread</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.unread}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex items-center">
                            <div className="p-3 rounded-full bg-blue-100">
                                <UserIcon className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-500">Conversations</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.conversations}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Messages Layout */}
                <div className="bg-white shadow rounded-lg overflow-hidden">
                    <div className="flex h-96">
                        {/* Conversations List */}
                        <div className="w-1/3 border-r border-gray-200">
                            <div className="p-4 border-b border-gray-200">
                                <h3 className="text-lg font-medium text-gray-900">Conversations</h3>
                            </div>
                            <div className="overflow-y-auto h-full">
                                {conversations.map((conversation: any) => (
                                    <div key={conversation.user.id} className="p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                <div className="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center">
                                                    <span className="text-white font-medium text-sm">
                                                        {conversation.user.name.charAt(0).toUpperCase()}
                                                    </span>
                                                </div>
                                                <div className="ml-3 flex-1">
                                                    <div className="flex items-center justify-between">
                                                        <p className="text-sm font-medium text-gray-900">{conversation.user.name}</p>
                                                        <p className="text-xs text-gray-500">{formatDate(conversation.lastMessage.created_at)}</p>
                                                    </div>
                                                    <div className="flex items-center text-xs text-gray-500 mt-1">
                                                        <HomeIcon className="h-3 w-3 mr-1" />
                                                        {conversation.property.title}
                                                    </div>
                                                    <p className="text-sm text-gray-600 truncate mt-1">
                                                        {conversation.lastMessage.message}
                                                    </p>
                                                </div>
                                            </div>
                                            {conversation.unreadCount > 0 && (
                                                <div className="ml-2">
                                                    <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full">
                                                        {conversation.unreadCount}
                                                    </span>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Message View */}
                        <div className="flex-1 flex flex-col">
                            <div className="p-4 border-b border-gray-200">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h3 className="text-lg font-medium text-gray-900">Select a conversation</h3>
                                        <p className="text-sm text-gray-500">Choose a conversation to view messages</p>
                                    </div>
                                    <button className="p-2 text-gray-400 hover:text-gray-600">
                                        <EllipsisVerticalIcon className="h-5 w-5" />
                                    </button>
                                </div>
                            </div>

                            <div className="flex-1 p-4 flex items-center justify-center">
                                <div className="text-center">
                                    <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400" />
                                    <h3 className="mt-2 text-sm font-medium text-gray-900">No conversation selected</h3>
                                    <p className="mt-1 text-sm text-gray-500">Select a conversation from the list to start messaging.</p>
                                </div>
                            </div>

                            {/* Message Input */}
                            <div className="p-4 border-t border-gray-200">
                                <div className="flex items-center space-x-2">
                                    <input
                                        type="text"
                                        placeholder="Type your message..."
                                        className="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        disabled
                                    />
                                    <button
                                        className="bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
                                        disabled
                                    >
                                        <PaperAirplaneIcon className="h-5 w-5" />
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {conversations.length === 0 && (
                    <div className="text-center py-12">
                        <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No messages yet</h3>
                        <p className="mt-1 text-sm text-gray-500">Messages from tenants and prospects will appear here.</p>
                    </div>
                )}
            </div>
        </DashboardLayout>
    );
}
